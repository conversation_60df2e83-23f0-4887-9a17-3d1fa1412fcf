import pandas as pd
import numpy as np
import itertools
import multiprocessing
from multiprocessing import shared_memory
import pickle
import psutil
import sys
import time
from device_configure import *
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from compact_storage import (
    SharedDataManager,
    PerformanceObjectPool
)

# 全局对象池实例
performance_pool = PerformanceObjectPool()

def generate_comprehensive_offset_cases_for_pagetype(page_data, state_cols, group_info, stress_col,
                                                   use_clustering=True, case_num = 5, num_clusters=6,
                                                   clustering_mode='bestoffset', bestoffset_data=None,
                                                   all_offset_combo_from_vendor=False,
                                                   vendor_RR_table=None):
    """
    为PageType层级生成5~10个综合offset组合，覆盖不同的PEC/Stress条件

    性能优化版本：
    - 共享内存优化：减少多进程数据传输开销
    - 紧凑数据结构：使用numpy数组减少内存占用
    - 对象池优化：减少字典创建开销
    - 渐进式内存清理：在关键节点释放内存
    - 分段处理策略：将大量组合分段处理，避免内存峰值
    - wl_key到wl_idx转换：大幅减少内存占用（60-80%内存优化）

    Args:
        page_data: PageType层级的数据，包含多个PEC和Stress条件，state只包含当前PageType的State
        state_cols: State列列表
        group_info: 分组信息
        stress_col: Stress列名
        use_clustering: 是否使用聚类方法替代itertools.product
        num_clusters: WL聚类数量
        clustering_mode: 聚类模式 ('bestoffset'推荐 或 'fitting_data'精确)
        bestoffset_data: bestoffset数据DataFrame（当clustering_mode='bestoffset'时使用）
        all_offset_combo_from_vendor: 是否使用vendor提供的RR table生成offset组合
        vendor_RR_table: vendor提供的RR table数据

    Returns:
        list: 综合offset组合结果列表
    """
    import gc  # 导入垃圾回收模块用于内存管理

    try:
        progressive_memory_cleanup("enter generate_comprehensive_offset_cases_for_pagetype function")
        # --------------------------------------------------------------------------
        # STAGE 0: 为每个PEC构建独立的WL映射 (PEC-Specific Vector Spaces)
        # --------------------------------------------------------------------------
        print("              \033[36m🔄 Stage 0: Building PEC-specific WL mappings...\033[0m")
        unique_pecs = page_data['PEC'].unique()
        unique_stress = page_data[stress_col].unique()

        # 收集所有PEC-Stress组合，为后续处理做准备
        pec_stress_combinations = []
        pec_stress_combos = []
        pec_wl_keys_map = {pec: set() for pec in unique_pecs}

        for pec in unique_pecs:
            for stress in unique_stress:
                # 内存优化：使用正确的query语法，用反引号包围列名。反引号告诉pandas将内容视为列名而不是Python标识符
                try:
                    pec_stress_data = page_data.query(f'PEC == {pec} and `{stress_col}` == {stress}')
                except:
                    # 如果query失败，回退到布尔索引
                    pec_stress_data = page_data[(page_data['PEC'] == pec) & (page_data[stress_col] == stress)]
                if not pec_stress_data.empty:
                    pec_stress_combinations.append((pec, stress, pec_stress_data))
                    pec_stress_combos.append((pec, stress))
                    # 为每个PEC收集其下所有的WL Keys
                    wl_keys = set(tuple(row) for row in pec_stress_data[["Channel", "Ce", "Lun", "Block", "WordLine"]].values)
                    pec_wl_keys_map[pec].update(wl_keys)

        # 基于每个PEC独立的WL Keys，构建各自的映射信息
        pec_info_map = {}
        for pec, wl_keys in pec_wl_keys_map.items():
            if not wl_keys: continue
            sorted_wl_keys = sorted(list(wl_keys))
            pec_info_map[pec] = {
                'wl_key_to_idx': {wl_key: idx for idx, wl_key in enumerate(sorted_wl_keys)},
                'idx_to_wl_key': {idx: wl_key for idx, wl_key in enumerate(sorted_wl_keys)},
                'num_wls': len(sorted_wl_keys)
            }
        
        del unique_pecs, unique_stress, pec_wl_keys_map
        progressive_memory_cleanup("Stage 0 completion")

        print(f"                \033[33m📋 Found {len(pec_stress_combinations)} PEC-Stress combinations\033[0m")
        print(f"                \033[33m📊 Built independent WL mappings for {len(pec_info_map)} PECs\033[0m")

        if not pec_stress_combinations:
            return []

        # --------------------------------------------------------------------------
        # STAGE 1: 收集所有候选offset组合并准备数据state_offset_wl_fbc_map
        # --------------------------------------------------------------------------
        print("              \033[36m🔄 Stage 1: Gathering all candidate combos and preparing data...\033[0m")
        master_combo_set = set()
        state_offset_wl_fbc_map = {}
        # 基于pec_info_map生成每个PEC的WL索引集合，用于后续评估
        pec_wl_idxs = {pec: set(range(info['num_wls'])) for pec, info in pec_info_map.items()}

        for pec, stress, pec_stress_data in pec_stress_combinations:
            print(f"                \033[32m🔧 Pre-processing PEC: {pec}, Stress: {stress}\033[0m")

            # 关键改动：传入对应PEC的wl_key_to_idx映射
            wl_key_to_idx = pec_info_map.get(pec, {}).get('wl_key_to_idx', {})
            state_offset_wl_fbc = build_state_offset_mapping(pec_stress_data, state_cols, wl_key_to_idx)

            # state_offset_wl_fbc_map: {(pec, stress): {state: {offset: {wl_idx: fbc}}}}
            state_offset_wl_fbc_map[(pec, stress)] = state_offset_wl_fbc

            # 获取每个PEC-Stress组合的bestoffset数据
            pec_stress_bestoffset_data = bestoffset_data[(bestoffset_data['PEC']==pec) & (bestoffset_data[stress_col]==stress)]

            # 生成offset组合
            if not all_offset_combo_from_vendor:
                # print(f"                  \033[33m📊 Generating in-house offset combinations\033[0m")
                if use_clustering:
                    try:
                        # 关键改动：传入对应PEC的idx_to_wl_key映射
                        idx_to_wl_key = pec_info_map.get(pec, {}).get('idx_to_wl_key', {})
                        all_wl_keys = set(idx_to_wl_key.values()) # 从映射中恢复wl_keys
                        
                        offset_combinations = generate_cluster_based_combinations(
                            pec_stress_combo = (pec, stress),
                            state_offset_wl_fbc = state_offset_wl_fbc,
                            idx_to_wl_key = idx_to_wl_key,
                            state_cols = state_cols,
                            all_wl_keys = all_wl_keys,
                            num_clusters=num_clusters,
                            bestoffset_data=pec_stress_bestoffset_data,
                            clustering_mode=clustering_mode
                        )
                        print(f"                  \033[36m✅ PEC: {pec}, Stress: {stress} - Generated {len(offset_combinations)} offset combinations using cluster-based method\033[0m")
                    except Exception as cluster_error:
                        print(f"                  \033[31m⚠️  Cluster-based generation failed: {cluster_error}, falling back to traditional method\033[0m")
                        del pec_stress_combinations, pec_wl_idxs, state_offset_wl_fbc_map, pec_stress_bestoffset_data, state_offset_wl_fbc
                        gc.collect()
                        return []
                    
                    # 添加到master set进行去重
                    for combo in offset_combinations:
                        master_combo_set.add(tuple(int(o) for o in combo))
                    offset_combinations = []  # 清空列表，避免后续重复处理
                else:
                    # 使用传统方法，但使用已优化的offset_lists（从bestoffset_data获取）
                    # 从bestoffset_data中获取每个state的offset列表，用于传统方法获取offset_combinations
                    offset_lists = []
                    estimated_combinations = 1

                    for state in state_cols:
                        if not pec_stress_bestoffset_data.empty:
                            # 内存优化：使用query方法获取state的offset列表
                            try:
                                state_data = pec_stress_bestoffset_data.query(f'State == "{state}"')
                            except:
                                state_data = pec_stress_bestoffset_data[pec_stress_bestoffset_data['State'] == state]

                            state_offsets = state_data['Offset'].dropna().unique()
                            state_offsets = sorted([int(o) for o in state_offsets if pd.notna(o)])
                            offset_lists.append(state_offsets if state_offsets else [0])

                            # 估算组合数量
                            estimated_combinations *= len(state_offsets)
                        else:
                            print(f"                  \033[33m📊 No bestoffset data found for PEC: {pec}, Stress: {stress}\033[0m")
                            del pec_stress_combinations, pec_wl_idxs, state_offset_wl_fbc_map, pec_stress_bestoffset_data, state_offset_wl_fbc
                            gc.collect()
                            return []
                    print(f"                  \033[33m📊 Estimated {estimated_combinations} combinations from bestoffset data\033[0m")

                    # 使用笛卡尔积生成offset组合
                    offset_combinations = list(itertools.product(*offset_lists))
                    
                    # 添加到master set() 进行去重
                    for combo in offset_combinations:
                        master_combo_set.add(tuple(int(o) for o in combo))
                    offset_combinations = []  # 清空列表，避免后续重复处理

                    # 及时释放offset_lists
                    del offset_lists
                    gc.collect()

            # 及时释放当前PEC-Stress的临时数据
            del pec_stress_bestoffset_data
            gc.collect()

        # 调用vendor提供的RR table，生成master_combo_set(与pec/stress无关)
        if all_offset_combo_from_vendor:
            print(f"                  \033[33m📊 Generating vendor offset combinations\033[0m")
            # 根据WLmode/WLtype/WLgroup/Partial_Prog生成master_combo_set
            WLmode = group_info['WLmode']
            WLtype = group_info['WLType']   #SLC is 'N/A'
            WLgroup = group_info['WLGroup'] #SLC/TLC is 'N/A'
            Partial_Prog = page_data['Partial_Prog'].unique()[0]   #如果使用group_info['Partial_Prog']，SLC/TLC is 'N/A'

            requred_cols = ['RRNumber'] + state_cols

            vendor_RR_table_filtered = vendor_RR_table[vendor_RR_table['WLmode'] == WLmode]
            if WLmode == 'TLC':
                if Partial_Prog == '100%':
                    vendor_RR_table_filtered = vendor_RR_table_filtered[vendor_RR_table_filtered['Condition'].str.contains('Close')]
                else:
                    vendor_RR_table_filtered = vendor_RR_table_filtered[vendor_RR_table_filtered['Condition'].str.contains('Open')]
                    if 'InnerWL' in WLtype:
                        vendor_RR_table_filtered = vendor_RR_table_filtered[vendor_RR_table_filtered['WLtype'].str.contains('InnerWL')]
                    else:
                        vendor_RR_table_filtered = vendor_RR_table_filtered[vendor_RR_table_filtered['WLtype'].str.contains('EdgeWL')]
            elif WLmode == 'QLC':
                if Partial_Prog == '100%':
                    vendor_RR_table_filtered = vendor_RR_table_filtered[vendor_RR_table_filtered['Condition'].str.contains('Close')]
                else:
                    vendor_RR_table_filtered = vendor_RR_table_filtered[~vendor_RR_table_filtered['Condition'].str.contains('Close')]
                    vendor_RR_table_filtered = vendor_RR_table_filtered[vendor_RR_table_filtered['WLgroup'] == WLgroup]
                    vendor_RR_table_filtered = vendor_RR_table_filtered[vendor_RR_table_filtered['WLtype'] == WLtype]

            final_filter_RR_table = vendor_RR_table_filtered[requred_cols]

            print(f"                  \033[33m📊 Final filter RR table with rr entries: {final_filter_RR_table['RRNumber'].unique()}\033[0m")

            offset_combinations = []
            offset_list = []
            for _, row in final_filter_RR_table.iterrows():
                for state in state_cols:
                    offset_list.append(row[state])
                offset_combinations.append(offset_list)
                offset_list = []

            for combo in offset_combinations:
                master_combo_set.add(tuple(int(o) for o in combo))
            offset_combinations = []  # 清空列表，避免后续重复处理

        # 确保zero combo在集合中，用于评估
        if master_combo_set:
            num_states = len(state_cols)
            zero_combo = tuple([0] * num_states)
            master_combo_set.add(zero_combo)
            print(f"                \033[33m📊 Total unique offset combinations to evaluate: {len(master_combo_set)}\033[0m")
        else:
            print(f"                \033[33m📊 No offset combinations to evaluate\033[0m")
            return []
        
        del page_data, pec_stress_combinations
        gc.collect()

        # 内存清理：Stage 1完成后的清理
        progressive_memory_cleanup("Stage 1 completion")

        # --------------------------------------------------------------------------
        # STAGE 2: 智能并行处理决策 + 分段处理，评估所有offset组合各自在单个PEC-Stress条件下的性能
        # --------------------------------------------------------------------------
        print("              \033[36m🔄 Stage 2: Starting intelligent parallel evaluation of all combos...\033[0m")

        # 分段处理配置
        combo_list = list(master_combo_set)
        total_combos = len(combo_list)

        # 硬编码每段处理5000个组合
        segment_size = 5000

        print(f"                \033[33m📊 Total combos: {total_combos}, Segment size: {segment_size}\033[0m")

        # 内存模式：直接在内存中累积结果
        all_offset_combinations = {}

        try:
            # 智能并行决策：基于数据大小和可用内存
            print("              \033[36m🧠 Analyzing memory requirements for parallel processing...\033[0m")

            # 估算主要数据结构的内存使用量
            data_size_gb = estimate_data_memory_usage(state_offset_wl_fbc_map)

            # 决定并行策略
            use_parallel, recommended_workers, decision_reason = decide_parallel_strategy(
                data_size_gb,
                max_workers=multiprocessing.cpu_count() - 2
            )

            print(f"                \033[36m🎯 Parallel Decision: {decision_reason}\033[0m")

            if use_parallel:
                # 并行处理模式
                print(f"                \033[32m🚀 Using parallel processing with {recommended_workers} workers\033[0m")

                # 共享内存优化：创建共享数据管理器
                shared_manager = SharedDataManager()

                try:
                    # 创建共享内存数据
                    shared_manager.create_shared_fbc_mapping(state_offset_wl_fbc_map)
                    # 关键改动：共享pec_info_map而不是pec_wl_idxs
                    shared_manager.create_shared_pec_info_map(pec_info_map)

                    # 分段处理所有组合
                    total_segments = (total_combos + segment_size - 1) // segment_size
                    print(f"                \033[33m📊 Processing {total_combos} combos in {total_segments} segments\033[0m")

                    used_cpu_count = recommended_workers

                    for segment_id in range(total_segments):
                        start_idx = segment_id * segment_size
                        end_idx = min(start_idx + segment_size, total_combos)
                        segment_combos = combo_list[start_idx:end_idx]

                        print(f"                \033[36m� Processing segment {segment_id + 1}/{total_segments} ({len(segment_combos)} combos)...\033[0m")

                        # 为当前分段准备任务批次
                        batch_size = max(50, len(segment_combos) // (used_cpu_count * 4)) #*4是为了生成更多小批次，imap_unordered会动态分配，快的进程会处理更多批次
                        print(f"                \033[36m🔄 Batch size {batch_size} with {used_cpu_count} cores for segment {segment_id + 1}/{total_segments}...\033[0m")
                        tasks = []
                        for i in range(0, len(segment_combos), batch_size):
                            combo_batch = segment_combos[i:i + batch_size]
                            task = (combo_batch, pec_stress_combos, state_cols, shared_manager.metadata)
                            tasks.append(task)

                        # 处理当前分段
                        segment_results = {}
                        if tasks:
                            with multiprocessing.Pool(used_cpu_count) as pool: # 创建N个独立的worker进程

                                # results_iterator 是一个惰性迭代器：
                                # - 不会阻塞等待所有任务完成
                                # - 每当有一个worker进程完成任务，就立即yield结果
                                # - 结果返回顺序是无序的（unordered），谁先完成谁先返回
                                results_iterator = pool.imap_unordered(evaluate_combo_batch_shared, tasks)  #异步分发任务并返回结果迭代器

                                from tqdm import tqdm
                                # 使用更稳定的手动更新模式
                                with tqdm(total=len(tasks), desc=f"{time.strftime('%Y-%m-%d %H:%M:%S')} Segment {segment_id + 1}/{total_segments}", ncols=100) as pbar:
                                    for batch_results in results_iterator:
                                        if batch_results:
                                            for combo, pec, stress, performance in batch_results:
                                                if combo not in segment_results:
                                                    segment_results[combo] = {}
                                                segment_results[combo][(pec, stress)] = performance
                                        pbar.update(1)

                        # 内存模式：累积到总结果中
                        all_offset_combinations.update(segment_results)
                        del segment_results
                        print(f"                \033[32m🧠 Segment {segment_id + 1} accumulated in memory ({len(all_offset_combinations)} total combos)\033[0m")

                        # 清理任务列表
                        del tasks
                        progressive_memory_cleanup(f"Segment {segment_id + 1} completion")

                    # 释放组合列表
                    progressive_memory_cleanup("All segments processing completion")

                finally:
                    # 确保共享内存资源被正确清理
                    shared_manager.cleanup()

            else:
                # 串行处理模式 - 简化版本，无需分段
                print(f"                \033[33m🐌 Using serial processing (single-threaded)\033[0m")
                print(f"                \033[33m📊 Processing {total_combos} combos directly (no segmentation needed)\033[0m")

                # 直接处理所有组合
                from tqdm import tqdm
                total_operations = total_combos * len(pec_stress_combos)

                with tqdm(total=total_operations, desc=f"{time.strftime('%Y-%m-%d %H:%M:%S')} Serial Processing", ncols=100) as pbar:
                    for combo in combo_list:
                        for pec, stress in pec_stress_combos:
                            if (pec, stress) not in state_offset_wl_fbc_map or not state_offset_wl_fbc_map[(pec, stress)]:
                                pbar.update(1)
                                continue

                            # 从pec_info_map获取当前PEC的WL信息
                            pec_info = pec_info_map.get(pec)
                            if not pec_info:
                                pbar.update(1)
                                continue

                            wl_idxs = set(range(pec_info['num_wls']))
                            num_unique_wls = pec_info['num_wls']
                            state_offset_wl_fbc = state_offset_wl_fbc_map[(pec, stress)]

                            # 调用原始的单个combo评估逻辑
                            performance = evaluate_single_combo(combo, pec, stress, state_cols, wl_idxs, state_offset_wl_fbc, num_unique_wls)
                            if performance is not None:
                                if combo not in all_offset_combinations:
                                    all_offset_combinations[combo] = {}
                                all_offset_combinations[combo][(pec, stress)] = performance

                            pbar.update(1)

                print(f"                \033[32m✅ Serial processing completed: {len(all_offset_combinations)} total combos\033[0m")
                progressive_memory_cleanup("Serial processing completion")

            # 清理主要数据结构
            del master_combo_set, combo_list

        except Exception as e:
            print(f"                \033[31m❌ Error in segmented processing: {e}\033[0m")
            raise
        
        # --------------------------------------------------------------------------
        # STAGE 3: 加载分段结果并选择最佳offset组合
        # --------------------------------------------------------------------------

        print("              \033[36m🔄 Stage 3: Selecting best combos using coverage-optimized algorithm...\033[0m")

        if case_num > 0:
            target_count = case_num
        else:
            target_count = len(all_offset_combinations)

        print(f"                \033[33m� Loaded {len(all_offset_combinations)} combos, selecting {target_count} best combinations\033[0m")
        comprehensive_combos, union_coverage_stats = select_comprehensive_offset_combinations(all_offset_combinations, pec_stress_combos, pec_info_map, target_count=target_count)

        print(f"                \033[32m✅ Selected {len(comprehensive_combos)} comprehensive offset combinations\033[0m")

        # 释放all_offset_combinations以节省内存
        del all_offset_combinations
        progressive_memory_cleanup("Stage 3 completion")
        
        # --------------------------------------------------------------------------
        # STAGE 4: 保存每个offset组合的详细结果以及联合覆盖率统计结果
        # --------------------------------------------------------------------------
        results = []
        for combo_info in comprehensive_combos:
            combo = combo_info['combo']

            # 为所有PEC-Stress条件创建行，确保每个条件都有PageFBC统计信息
            pec_stress_performance = combo_info.get('pec_stress_performance', {})

            # 遍历所有PEC-Stress条件
            for pec, stress in pec_stress_combos:

                # 创建该PEC-Stress条件的详细记录
                detailed_result = {}

                if (pec, stress) in pec_stress_performance:
                    # 如果该offset组合评估过这个条件，使用评估结果
                    perf = pec_stress_performance[(pec, stress)]
                    # 动态计算覆盖率统计（内存优化）
                    # 按需计算covered_keys以节省内存
                    covered_indices = get_covered_indices_dynamic(perf.get('fbc_vector'), FBC_criterion)
                    covered_count = len(covered_indices)
                    total_wl_count = perf.get('total_wl_count', 0)
                    coverage_ratio = covered_count / total_wl_count if total_wl_count > 0 else 0

                    detailed_result.update({
                        **group_info,
                        'OffsetCombo': combo,
                        'PEC': pec,  # 分离PEC列
                        'Stress': stress,  # 分离Stress列
                        'PageFBC_max': perf.get('all_fbc_max', 0),  # 移到Stress后面
                        'PageFBC_mean': f"{perf.get('all_fbc_mean', 0):.1f}",  # 移到Stress后面
                        'Total_WL_Count': total_wl_count,
                        'Covered_WL_Count': covered_count,
                        'Coverage_Ratio': f"{coverage_ratio:.2%}"  # 动态计算的覆盖率
                    })
                else:
                    print(f"                  \033[33m⚠️  No performance data found for PEC: {pec}, Stress: {stress}\033[0m")
                    return []

                results.append(detailed_result)

        # 添加联合覆盖率统计结果
        for (pec, stress), union_stat in union_coverage_stats.items():
            union_result = {
                **group_info,
                'OffsetCombo': 'AllCombos',
                'PEC': pec,
                'Stress': stress,  # 分离Stress列
                'PageFBC_max': union_stat.get('union_all_fbc_max', ''),  # 放在Stress后面
                'PageFBC_mean': union_stat.get('union_all_fbc_mean', ''),  # 放在Stress后面
                'Total_WL_Count': union_stat['union_total_count'],
                'Covered_WL_Count': union_stat['union_covered_count'],
                'Coverage_Ratio': f"{union_stat['union_coverage_ratio']:.2%}"  # 联合覆盖率映射到Coverage_Ratio
            }
            results.append(union_result)

        # 最终清理内存和性能统计
        del comprehensive_combos, union_coverage_stats
        del state_offset_wl_fbc_map

        # 输出性能优化统计信息
        pool_stats = performance_pool.get_stats()
        print(f"              \033[35m📊 Performance Pool Stats: Created={pool_stats['created']}, Reused={pool_stats['reused']}, Pool Size={pool_stats['pool_size']}\033[0m")

        # 最终内存清理
        progressive_memory_cleanup("Function completion")

        return results

    except Exception as e:
        print(f"              \033[31m❌ Error generating comprehensive offset cases for PageType: {e}\033[0m")
        # 异常情况下也要清理内存
        try:
            del pec_stress_combinations, state_offset_wl_fbc_map
            gc.collect()
        except:
            pass
        return []


def build_state_offset_mapping(region_df, state_cols, wl_key_to_idx=None):
    """
    Build state-offset-wl-fbc mapping (高度优化版本 + wl_idx内存优化)
    适配长表格式数据（包含Condition列而不是State列）

    优化策略：
    1. 数据类型优化：使用更小的数据类型减少内存占用
    2. 向量化操作：减少Python循环，提升处理速度
    3. 一次性分组：避免重复DataFrame操作
    4. 内存管理：及时释放临时对象，减少GC压力
    5. wl_key到wl_idx转换：大幅减少内存占用（60-80%优化）

    Args:
        region_df (pd.DataFrame): Regional dataframe (长表格式，包含Condition列)
        state_cols (list): List of State columns (如['State01', 'State07', 'State13'])
        wl_key_to_idx (dict): WL key到index的映射，用于内存优化

    Returns:
        dict: {state: {offset: {wl_idx: fbc}}} if wl_key_to_idx provided, else {state: {offset: {wl_key: fbc}}}
    """
    import gc  # 导入垃圾回收模块

    print(f"                  \033[36m🔧 Building state-offset mapping for {len(state_cols)} states...\033[0m")

    state_offset_wl_fbc = {state: {} for state in state_cols}

    # 优化1: 预先选择和优化数据类型，减少内存占用
    required_cols = ["Channel", "Ce", "Lun", "Block", "WordLine", "Offset", "FBC", "State"]
    region_df_subset = region_df[required_cols].copy()

    # 优化2: 一次性删除NaN行，避免后续重复检查
    region_df_subset = region_df_subset.dropna(subset=['FBC', 'Offset'])

    # 优化3: 预先构建WL键，避免重复计算
    wl_cols = ["Channel", "Ce", "Lun", "Block", "WordLine"]
    region_df_subset['wl_key'] = region_df_subset[wl_cols].apply(tuple, axis=1)

    # 优化3.5: 如果提供了wl_key_to_idx映射，转换为wl_idx以节省内存
    if wl_key_to_idx is not None:
        # print(f"                  \033[35m🧠 Converting wl_key to wl_idx for memory optimization...\033[0m")
        region_df_subset['wl_idx'] = region_df_subset['wl_key'].map(wl_key_to_idx)
        # 过滤掉无法映射的wl_key（如果有的话）
        region_df_subset = region_df_subset.dropna(subset=['wl_idx'])
        region_df_subset['wl_idx'] = region_df_subset['wl_idx'].astype(int)  # 确保是整数类型
        key_column = 'wl_idx'
        del region_df_subset['wl_key']
    else:
        key_column = 'wl_key'

    # 优化4: 一次性分组，避免重复操作
    # print(f"                  \033[36m🔧 Performing vectorized groupby operation with {key_column}...\033[0m")
    grouped = region_df_subset.groupby(['State', 'Offset'], observed=True)

    # 优化5: 批量处理分组结果，减少Python循环开销
    processed_groups = 0
    for (state, offset), group in grouped:
        if state in state_cols:
            if offset not in state_offset_wl_fbc[state]:
                state_offset_wl_fbc[state][offset] = {}

            # 优化6: 向量化构建字典，避免逐行处理，使用wl_idx或wl_key作为键
            wl_fbc_dict = dict(zip(group[key_column], group['FBC']))
            state_offset_wl_fbc[state][offset] = wl_fbc_dict

            processed_groups += 1

    # print(f"                  \033[32m✅ Processed {processed_groups} state-offset groups\033[0m")

    # 优化7: 及时清理临时数据，减少内存峰值
    del region_df_subset, grouped
    gc.collect()

    return state_offset_wl_fbc


def get_memory_usage():
    """获取当前内存使用量（MB）"""
    import psutil
    import os
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024


def progressive_memory_cleanup(stage_name):
    """
    渐进式内存清理策略
    在关键处理节点进行内存清理，减少内存峰值和GC压力

    Args:
        stage_name: 当前处理阶段名称
        local_vars_to_clean: 需要清理的局部变量名列表
    """
    import gc

    # 强制垃圾回收
    collected = gc.collect()

    # 获取内存使用情况
    memory_mb = get_memory_usage()

    print(f"                \033[35m🧠 Memory after {stage_name}: {memory_mb:.1f} MB (GC collected: {collected} objects)\033[0m")

    return memory_mb

def get_memory_info():
    """
    获取系统内存信息

    Returns:
        tuple: (总内存GB, 可用内存GB, 内存使用率%)
    """
    try:
        memory = psutil.virtual_memory()
        total_gb = memory.total / (1024**3)
        available_gb = memory.available / (1024**3)
        usage_percent = memory.percent
        return total_gb, available_gb, usage_percent
    except Exception as e:
        print(f"                \033[31m❌ Error getting memory info: {e}\033[0m")
        return 0, 0, 100

def estimate_data_memory_usage(data_dict):
    """
    估算数据结构的内存使用量

    Args:
        data_dict: 要估算的数据字典

    Returns:
        float: 估算的内存使用量(GB)
    """
    try:
        # 使用sys.getsizeof递归估算
        def get_size(obj, seen=None):
            size = sys.getsizeof(obj)
            if seen is None:
                seen = set()

            obj_id = id(obj)
            if obj_id in seen:
                return 0

            # 标记为已访问
            seen.add(obj_id)

            if isinstance(obj, dict):
                size += sum([get_size(v, seen) for v in obj.values()])
                size += sum([get_size(k, seen) for k in obj.keys()])
            elif hasattr(obj, '__dict__'):
                size += get_size(obj.__dict__, seen)
            elif hasattr(obj, '__iter__') and not isinstance(obj, (str, bytes, bytearray)):
                size += sum([get_size(i, seen) for i in obj])

            return size

        total_bytes = get_size(data_dict)
        total_gb = total_bytes / (1024**3)
        return total_gb
    except Exception as e:
        print(f"                \033[31m❌ Error estimating memory usage: {e}\033[0m")
        return 0

def decide_parallel_strategy(data_size_gb, max_workers=None):
    """
    基于数据大小和可用内存决定并行策略

    Args:
        data_size_gb: 数据大小(GB)
        max_workers: 最大worker数量

    Returns:
        tuple: (是否使用并行, 推荐worker数量, 决策原因)
    """
    try:
        total_memory_gb, available_memory_gb, usage_percent = get_memory_info()

        print(f"                \033[36m🧠 Memory Analysis:\033[0m")
        print(f"                \033[36m   - Total Memory: {total_memory_gb:.1f} GB\033[0m")
        print(f"                \033[36m   - Available Memory: {available_memory_gb:.1f} GB\033[0m")
        print(f"                \033[36m   - Current Usage: {usage_percent:.1f}%\033[0m")
        print(f"                \033[36m   - Data Size: {data_size_gb:.1f} GB\033[0m")

        # 安全系数：为系统预留内存
        safety_factor = 0.35  # 安全的可用内存
        safe_available_memory = available_memory_gb * safety_factor

        print(f"                \033[36m   - Safe Available Memory: {safe_available_memory:.1f} GB\033[0m")

        # 估算并行处理的内存需求
        # 主进程保持原始数据 + 每个worker进程都会反序列化完整数据
        if max_workers is None:
            max_workers = multiprocessing.cpu_count() - 2

        # 找到最大可用的worker数量
        best_workers = 0
        best_memory_need = 0

        for workers in range(1, max_workers + 1):
            # 内存需求 = 主进程数据 + workers * 每个进程的数据副本
            estimated_memory_need = data_size_gb * (1 + workers)

            if estimated_memory_need <= safe_available_memory:
                best_workers = workers
                best_memory_need = estimated_memory_need
                # 继续检查更多worker是否可行
            else:
                # 超出内存限制，停止检查
                break

        if best_workers > 0:
            reason = f"Optimal parallel with {best_workers} workers (need {best_memory_need:.1f}GB, have {safe_available_memory:.1f}GB)"
            print(f"                \033[32m✅ {reason}\033[0m")
            return True, best_workers, reason

        # 如果连1个worker都不行，使用串行处理
        min_parallel_need = data_size_gb * 2  # 主进程 + 1个worker
        reason = f"Memory insufficient for parallel (need {min_parallel_need:.1f}GB+, have {safe_available_memory:.1f}GB)"
        print(f"                \033[33m⚠️  {reason}\033[0m")
        return False, 1, reason

    except Exception as e:
        reason = f"Error in memory analysis: {e}, fallback to serial"
        print(f"                \033[31m❌ {reason}\033[0m")
        return False, 1, reason


def cluster_wls_by_bestoffset(bestoffset_data, state_cols, num_clusters=6):
    """
    基于bestoffset数据进行WL聚类
    聚类特征：各state的best_offset值、WordLine位置

    改进要点：
    - 去除avg_offset和offset_std统计特征，避免被异常值影响
    - 去除Block特征，专注于最有区分度的特征
    - 保持各state的独立性，避免本质不相似的WL被错误聚类
    - 输出带cluster列的CSV文件供分析

    Args:
        bestoffset_data: bestoffset数据DataFrame
        state_cols: State列列表
        num_clusters: 聚类数量

    Returns:
        dict: {cluster_id: [wl_keys]}
    """
    print(f"                      \033[36m🔄 Using bestoffset-based clustering for {len(bestoffset_data)} WLs\033[0m")
    print(f"                      \033[33m📋 Features: {state_cols} + WordLine (no statistical features)\033[0m")

    # 收集WL特征
    features = []
    wl_list = []

    for _, row in bestoffset_data.iterrows():
        wl_key = (row['Channel'], row['Ce'], row['Lun'], row['Block'], row['WordLine'])
        wl_list.append(wl_key)

        # 特征1-N：各state的best offset值（保持独立性）
        state_offsets = []
        for state in state_cols:
            if state in row and pd.notna(row[state]):
                state_offsets.append(row[state])
            else:
                state_offsets.append(0)  # 默认值

        # 特征N+1：WordLine位置（反映物理特性）
        wordline = row['WordLine'] if 'WordLine' in row else 0

        # 最终特征向量：[State01_offset, State07_offset, State13_offset, WordLine]
        # 简洁、直接、避免统计偏差
        features.append(state_offsets + [wordline])

    if len(wl_list) < num_clusters:
        # 如果WL数量少于聚类数，每个WL一个聚类
        return {i: [wl] for i, wl in enumerate(wl_list)}

    # K-means聚类
    try:
        scaler = StandardScaler()   #对所有特征进行标准化处理，使它们的均值为0，方差为1，消除量纲的影响。
        features_scaled = scaler.fit_transform(features)    # 对特征进行标准化处理

        kmeans = KMeans(n_clusters=num_clusters, random_state=42, n_init=10)  # 使用K-means算法进行聚类
        cluster_labels = kmeans.fit_predict(features_scaled)  # 对标准化后的特征进行聚类

        # 组织聚类结果
        clusters = {i: [] for i in range(num_clusters)}
        for wl_key, label in zip(wl_list, cluster_labels):
            clusters[label].append(wl_key)

        cluster_sizes = [len(wls) for wls in clusters.values() if wls]
        print(f"                      \033[32m✅ Bestoffset-based clustering completed: {len(cluster_sizes)} clusters with sizes {cluster_sizes}\033[0m")

        # 输出聚类特征分析（用于调试）
        print(f"                      \033[36m📊 Feature dimensions: {len(state_cols)} state offsets + 1 WordLine = {len(state_cols) + 1} total\033[0m")

        if 1:
            # 添加cluster列到bestoffset_data并输出CSV
            bestoffset_with_cluster = bestoffset_data.copy()

            # 创建WL key到cluster的映射
            wl_to_cluster = {}
            for cluster_id, wl_keys in clusters.items():
                for wl_key in wl_keys:
                    wl_to_cluster[wl_key] = cluster_id

            # 为每行数据添加cluster列
            cluster_column = []
            for _, row in bestoffset_with_cluster.iterrows():
                wl_key = (row['Channel'], row['Ce'], row['Lun'], row['Block'], row['WordLine'])
                cluster_column.append(wl_to_cluster.get(wl_key, -1))  # -1表示未分配到任何cluster

            bestoffset_with_cluster['Cluster'] = cluster_column

            # 输出CSV文件（自动判断是否是第一次调用）
            import os
            output_filename = 'bestoffset_data_with_clusters1.csv'

            # 检查文件是否存在来判断是否是第一次调用
            is_first_call = not os.path.exists(os.path.join(output_dir, output_filename))
            mode = 'w' if is_first_call else 'a'
            header = is_first_call

            bestoffset_with_cluster.to_csv(os.path.join(output_dir, output_filename), mode=mode, header=header, index=False)
            action = "saved to" if is_first_call else "appended to"
            print(f"                      \033[32m💾 Clustering results {action}: {output_filename}\033[0m")
            print(f"                      \033[36m📋 CSV contains {len(bestoffset_with_cluster)} rows with cluster assignments\033[0m")

        return clusters

    except Exception as e:
        print(f"                      \033[31m⚠️  Bestoffset clustering failed: {e}, using simple grouping\033[0m")
        # 如果聚类失败，使用简单分组
        cluster_size = len(wl_list) // num_clusters + 1
        clusters = {}
        for i in range(num_clusters):
            start_idx = i * cluster_size
            end_idx = min((i + 1) * cluster_size, len(wl_list))
            if start_idx < len(wl_list):
                clusters[i] = wl_list[start_idx:end_idx]

        if 1:
            # 为简单分组也输出CSV
            bestoffset_with_cluster = bestoffset_data.copy()

            # 创建WL key到cluster的映射
            wl_to_cluster = {}
            for cluster_id, wl_keys in clusters.items():
                for wl_key in wl_keys:
                    wl_to_cluster[wl_key] = cluster_id

            # 为每行数据添加cluster列
            cluster_column = []
            for _, row in bestoffset_with_cluster.iterrows():
                wl_key = (row['Channel'], row['Ce'], row['Lun'], row['Block'], row['WordLine'])
                cluster_column.append(wl_to_cluster.get(wl_key, -1))  # -1表示未分配到任何cluster

            bestoffset_with_cluster['Cluster'] = cluster_column

            # 输出CSV文件（自动判断是否是第一次调用）
            import os
            output_filename = 'bestoffset_data_with_clusters.csv'

            # 检查文件是否存在来判断是否是第一次调用
            is_first_call = not os.path.exists(os.path.join(output_dir, output_filename))
            mode = 'w' if is_first_call else 'a'
            header = is_first_call

            bestoffset_with_cluster.to_csv(os.path.join(output_dir, output_filename), mode=mode, header=header, index=False)
            action = "saved to" if is_first_call else "appended to"
            print(f"                      \033[32m💾 Simple clustering results {action}: {output_filename}\033[0m")

        return clusters


def cluster_wls_by_bestoffset_fbc_enhanced(bestoffset_data, state_cols, num_clusters=6):
    """
    FBC增强的多维行为聚类 (FBC-Enhanced Behavioral Clustering)

    核心思想: 彻底抛弃只使用Offset的单一视角。我们将每个WordLine在一个State下的行为定义为一个二维特征 (Offset, FBC)。
    通过将所有State的这些二维特征拼接起来，我们构建一个能全面描述WL特性的高维特征向量，并基于此进行聚类。

    新特征向量: 对于一个有N个State的系统，每个WL的最终特征向量将是：
    [offset_s1, fbc_s1, offset_s2, fbc_s2, ..., offset_sN, fbc_sN]
    这个向量的维度是 2 * N。

    Args:
        bestoffset_data: bestoffset数据DataFrame (长表格式，包含State, Offset, FBC列)
        state_cols: State列列表
        num_clusters: 聚类数量

    Returns:
        dict: {cluster_id: [wl_keys]}
    """
    print(f"                      \033[36m🔄 Using FBC-Enhanced Behavioral Clustering for {len(bestoffset_data.drop_duplicates(subset=['Channel', 'Ce', 'Lun', 'Block', 'WordLine']))} WLs\033[0m")
    print(f"                      \033[33m📋 Features: (Offset, FBC) pairs for states {state_cols}\033[0m")

    # 步骤1: 重构数据，为每个WL构建完整的特征向量
    # 首先获取所有唯一的WL
    unique_wls = bestoffset_data[['Channel', 'Ce', 'Lun', 'Block', 'WordLine']].drop_duplicates()

    features = []
    wl_list = []

    for _, wl_row in unique_wls.iterrows():
        wl_key = (wl_row['Channel'], wl_row['Ce'], wl_row['Lun'], wl_row['Block'], wl_row['WordLine'])
        wl_list.append(wl_key)

        # 为当前WL构建特征向量: [offset_s1, fbc_s1, offset_s2, fbc_s2, ...]
        wl_feature_vector = []

        for state in state_cols:
            # 查找当前WL在当前State下的数据
            wl_state_data = bestoffset_data[
                (bestoffset_data['Channel'] == wl_row['Channel']) &
                (bestoffset_data['Ce'] == wl_row['Ce']) &
                (bestoffset_data['Lun'] == wl_row['Lun']) &
                (bestoffset_data['Block'] == wl_row['Block']) &
                (bestoffset_data['WordLine'] == wl_row['WordLine']) &
                (bestoffset_data['State'] == state)
            ]

            if not wl_state_data.empty:
                # 获取该WL在该State下的Offset和FBC值
                offset_val = wl_state_data['Offset'].iloc[0]
                fbc_val = wl_state_data['FBC'].iloc[0]

                # 确保数值有效
                if pd.notna(offset_val) and pd.notna(fbc_val):
                    wl_feature_vector.extend([float(offset_val), float(fbc_val)])
                else:
                    # 如果数据缺失，使用默认值
                    wl_feature_vector.extend([0.0, 240.0])  # 默认offset=0, 默认FBC=240(较差值)
            else:
                # 如果该WL在该State下没有数据，使用默认值
                wl_feature_vector.extend([0.0, 240.0])

        features.append(wl_feature_vector)

    if len(wl_list) < num_clusters:
        # 如果WL数量少于聚类数，每个WL一个聚类
        return {i: [wl] for i, wl in enumerate(wl_list)}

    # 步骤2: 标准化处理 - 由于Offset和FBC的数值范围差异巨大，标准化处理消除差异
    try:
        # print(f"                      \033[33m📊 Feature vector dimension: {len(features[0])} (2 * {len(state_cols)} states)\033[0m")
        # Offset range: [-20, 20], FBC range: [0, 240] - StandardScaler is critical!
        scaler = StandardScaler()   #创建一个"数据标准化器"，使特征的均值为0，方差为1，消除量纲的影响。减去均值，然后除以标准差
        features_scaled = scaler.fit_transform(features)    # 对特征进行标准化处理

        # 步骤3: K-means聚类
        kmeans = KMeans(n_clusters=num_clusters, random_state=42, n_init=10)
        cluster_labels = kmeans.fit_predict(features_scaled)

        # 组织聚类结果
        clusters = {i: [] for i in range(num_clusters)}
        for wl_key, label in zip(wl_list, cluster_labels):
            clusters[label].append(wl_key)  # 将每个wl_key分配到对应的聚类中

        cluster_sizes = [len(wls) for wls in clusters.values() if wls]
        print(f"                      \033[32m✅ FBC-Enhanced Behavioral Clustering completed: {len(cluster_sizes)} clusters with sizes {cluster_sizes}\033[0m")
        # print(f"                      \033[36m📊 Feature analysis: Each WL characterized by {len(state_cols)} (Offset,FBC) pairs\033[0m")

        if 1:
            # 添加cluster列到bestoffset_data并输出CSV
            bestoffset_with_cluster = bestoffset_data.copy()

            # 创建WL key到cluster的映射
            wl_to_cluster = {}
            for cluster_id, wl_keys in clusters.items():
                for wl_key in wl_keys:
                    wl_to_cluster[wl_key] = cluster_id

            # 为每行数据添加cluster列
            cluster_column = []
            for _, row in bestoffset_with_cluster.iterrows():
                wl_key = (row['Channel'], row['Ce'], row['Lun'], row['Block'], row['WordLine'])
                cluster_column.append(wl_to_cluster.get(wl_key, -1))  # -1表示未分配到任何cluster

            bestoffset_with_cluster['Cluster'] = cluster_column

            # 输出CSV文件（自动判断是否是第一次调用）
            import os
            output_filename = 'bestoffset_data_with_clusters2.csv'

            # 检查文件是否存在来判断是否是第一次调用
            is_first_call = not os.path.exists(os.path.join(output_dir, output_filename))
            mode = 'w' if is_first_call else 'a'
            header = is_first_call

            bestoffset_with_cluster.to_csv(os.path.join(output_dir, output_filename), mode=mode, header=header, index=False)
            action = "saved to" if is_first_call else "appended to"
            print(f"                      \033[32m💾 Clustering results {action}: {output_filename}\033[0m")
            # print(f"                      \033[36m📋 CSV contains {len(bestoffset_with_cluster)} rows with cluster assignments\033[0m")


        return clusters

    except Exception as e:
        print(f"                      \033[31m⚠️  FBC-Enhanced clustering failed: {e}, using simple grouping\033[0m")
        # 如果聚类失败，使用简单分组
        cluster_size = len(wl_list) // num_clusters + 1
        clusters = {}
        for i in range(num_clusters):
            start_idx = i * cluster_size
            end_idx = min((i + 1) * cluster_size, len(wl_list))
            if start_idx < len(wl_list):
                clusters[i] = wl_list[start_idx:end_idx]

        return clusters


def cluster_wls_by_fitting_data(pec_stress_combo,state_offset_wlkey_fbc, state_cols, num_clusters=6):
    """
    基于fitting_data进行WL聚类（改进的多维特征方法）

    聚类特征（简化且高效的设计）：
    1. 每个state的best_offset值（修复：原来错误地只用一个全局最优offset）
    2. 每个state的best_FBC值（保留完整信息，不使用统计特征）
    3. offset敏感性（FBC值变化的标准差，可选特征）

    Args:
        pec_stress_combo: PEC-Stress组合
        state_offset_wlkey_fbc: State-offset-WL-FBC映射 {state: {offset: {wl_key: fbc}}}
        state_cols: State列列表，如['State01', 'State07', 'State13', 'State15']
        num_clusters: 聚类数量

    Returns:
        dict: {cluster_id: [wl_keys]}

    特征向量维度：2 * len(state_cols) + 1
    - 前len(state_cols)维：各state的best_offset
    - 中len(state_cols)维：各state的best_fbc
    - 最后1维：offset_sensitivity
    """
    print(f"                      \033[36m🔄 Using fitting_data-based clustering for WL similarity analysis\033[0m")
    print(f"                      \033[36m📊 Feature dimensions: {len(state_cols)} best_offsets + {len(state_cols)} best_fbcs + 1 sensitivity = {2 * len(state_cols) + 1}D\033[0m")
    # 收集所有WL键
    all_wl_keys = set() #set是一个无序且不重复的集合

    # 提取PEC和Stress值用于CSV输出
    pec, stress = pec_stress_combo

    for state in state_cols:
        for offset_dict in state_offset_wlkey_fbc[state].values():
            all_wl_keys.update(offset_dict.keys()) 

    if len(all_wl_keys) < num_clusters:
        # 如果WL数量少于聚类数，每个WL一个聚类
        return {i: [wl] for i, wl in enumerate(all_wl_keys)}

    # 特征提取
    features = []
    wl_list = list(all_wl_keys)

    for wl_key in wl_list:
        # 特征1：每个state的best offset值（修复：为每个state分别找最优offset）
        best_offsets_per_state = []
        best_fbcs_per_state = []

        for state in state_cols:
            state_best_offset = None
            state_best_fbc = float('inf')

            # 为当前state找到该WL的最优offset
            if state in state_offset_wlkey_fbc:
                for offset, wl_fbc_dict in state_offset_wlkey_fbc[state].items():
                    if wl_key in wl_fbc_dict:
                        if wl_fbc_dict[wl_key] < state_best_fbc:
                            state_best_fbc = wl_fbc_dict[wl_key]
                            state_best_offset = offset

            # 如果该state没有找到数据，使用默认值
            if state_best_offset is None:
                state_best_offset = 0
                state_best_fbc = float('inf')

            best_offsets_per_state.append(state_best_offset)
            best_fbcs_per_state.append(state_best_fbc)

        # 特征2：各state的best FBC值（保留完整信息，不使用统计特征）
        # 对于缺失数据，使用一个较大的默认值以保持特征向量维度一致
        processed_best_fbcs = []
        for fbc in best_fbcs_per_state:
            if fbc == float('inf'):
                processed_best_fbcs.append(1000)  # 使用1000作为缺失数据的默认FBC值
            else:
                processed_best_fbcs.append(fbc)

        # 特征3：offset敏感性（FBC值的变化范围）- 可选特征
        all_fbc_values = []
        for state in state_cols:
            if state in state_offset_wlkey_fbc:
                for offset, wl_fbc_dict in state_offset_wlkey_fbc[state].items():
                    if wl_key in wl_fbc_dict:
                        all_fbc_values.append(wl_fbc_dict[wl_key])

        offset_sensitivity = np.std(all_fbc_values) if len(all_fbc_values) > 1 else 0

        # 构建特征向量：[各state的best_offset..., 各state的best_fbc..., offset_sensitivity]
        feature_vector = best_offsets_per_state + processed_best_fbcs + [offset_sensitivity]
        features.append(feature_vector)

    # K-means聚类
    try:
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features)

        kmeans = KMeans(n_clusters=num_clusters, random_state=42, n_init=10)
        cluster_labels = kmeans.fit_predict(features_scaled)

        # 组织聚类结果
        clusters = {i: [] for i in range(num_clusters)}
        for wl_key, label in zip(wl_list, cluster_labels):
            clusters[label].append(wl_key)

        if 1:
            # 创建WL key和cluster的简单映射表并输出CSV
            wl_cluster_data = []
            for cluster_id, wl_keys in clusters.items():
                for wl_key in wl_keys:
                    wl_cluster_data.append({
                        'Channel': wl_key[0],
                        'Ce': wl_key[1],
                        'Lun': wl_key[2],
                        'Block': wl_key[3],
                        'WordLine': wl_key[4],
                        'PEC': pec,
                        stress_col: stress,
                        'Cluster': cluster_id
                    })

            # 创建DataFrame
            wl_cluster_df = pd.DataFrame(wl_cluster_data)

            # 输出CSV文件（自动判断是否是第一次调用）
            import os
            output_filename = 'wl_cluster3_mapping_fitting_data.csv'

            # 检查文件是否存在来判断是否是第一次调用
            is_first_call = not os.path.exists(os.path.join(output_dir, output_filename))
            mode = 'w' if is_first_call else 'a'
            header = is_first_call

            wl_cluster_df.to_csv(os.path.join(output_dir, output_filename), mode=mode, header=header, index=False)
            action = "saved to" if is_first_call else "appended to"
            print(f"                      \033[32m💾 WL-Cluster mapping {action}: {output_filename}\033[0m")
            print(f"                      \033[36m📋 CSV contains {len(wl_cluster_df)} WL-cluster mappings\033[0m")


        return clusters

    except Exception as e:
        print(f"                      \033[31m⚠️  Clustering failed: {e}, using simple grouping\033[0m")
        # 如果聚类失败，使用简单分组
        cluster_size = len(wl_list) // num_clusters + 1
        clusters = {}
        for i in range(num_clusters):
            start_idx = i * cluster_size
            end_idx = min((i + 1) * cluster_size, len(wl_list))
            if start_idx < len(wl_list):
                clusters[i] = wl_list[start_idx:end_idx]

        if 1:   
            # 为简单分组也输出CSV
            wl_cluster_data = []
            for cluster_id, wl_keys in clusters.items():
                for wl_key in wl_keys:
                    wl_cluster_data.append({
                        'Channel': wl_key[0],
                        'Ce': wl_key[1],
                        'Lun': wl_key[2],
                        'Block': wl_key[3],
                        'WordLine': wl_key[4],
                        'PEC': pec,
                        stress_col: stress,
                        'Cluster': cluster_id
                    })

            # 创建DataFrame
            wl_cluster_df = pd.DataFrame(wl_cluster_data)

            # 输出CSV文件（自动判断是否是第一次调用）
            import os
            output_filename = 'wl_cluster_mapping_similarity.csv'

            # 检查文件是否存在来判断是否是第一次调用
            is_first_call = not os.path.exists(output_filename)
            mode = 'w' if is_first_call else 'a'
            header = is_first_call

            wl_cluster_df.to_csv(output_filename, mode=mode, header=header, index=False)
            action = "saved to" if is_first_call else "appended to"
            print(f"                      \033[32m💾 Simple WL-Cluster mapping {action}: {output_filename}\033[0m")

        return clusters


def get_diverse_state_offsets_for_cluster(cluster_wls, state_offset_wl_fbc, state_cols, top_n_by_fbc=2, bestoffset_data=None):
    """
    为单个聚类，获取其在每个State下的多样化候选Offset（最优+边界）。

    Args:
        cluster_wls (list): 聚类中的WL键列表。
        state_offset_wl_fbc (dict): 单个PEC-Stress下的State-offset-WL-FBC映射。
        state_cols (list): State列列表。
        top_n_by_fbc (int): 按平均FBC表现选出的最优Offset数量。
        bestoffset_data (DataFrame): bestoffset数据，用于获取边界offset。

    Returns:
        dict: {state: {offset1, offset2, ...}}
    """
    if not cluster_wls:
        return {}

    cluster_state_offsets = {}

    for state in state_cols:
        # 收集该聚类中所有WL在当前state下的所有可用offset及其平均FBC
        offset_performance = []
        if state in state_offset_wl_fbc:
            for offset, wl_fbc_dict in state_offset_wl_fbc[state].items():
                fbc_values = [wl_fbc_dict[wl] for wl in cluster_wls if wl in wl_fbc_dict and pd.notna(wl_fbc_dict[wl])]
                if fbc_values:
                    avg_fbc = np.mean(fbc_values)
                    offset_performance.append({'offset': offset, 'avg_fbc': avg_fbc})

        if not offset_performance:
            cluster_state_offsets[state] = {0}  # 如果没有有效offset，至少提供0 (Python int类型)
            continue

        candidate_offsets = set()

        # 策略1: 添加最优FBC的Top N个offset
        sorted_by_fbc = sorted(offset_performance, key=lambda x: x['avg_fbc'])
        for item in sorted_by_fbc[:top_n_by_fbc]:
            candidate_offsets.add(int(item['offset']))  # 转换为Python int类型

        # 策略2: 添加边界offset (min/max) - 从bestoffset_data获取
        state_offsets = bestoffset_data[bestoffset_data['State'] == state]['Offset'].dropna().unique()
        candidate_offsets.add(int(min(state_offsets)))  # 转换为Python int类型
        candidate_offsets.add(int(max(state_offsets)))  # 转换为Python int类型

        cluster_state_offsets[state] = candidate_offsets

    return cluster_state_offsets


def generate_cluster_based_combinations(pec_stress_combo, state_offset_wl_fbc, idx_to_wl_key, state_cols, all_wl_keys, num_clusters=6,
                                      bestoffset_data=None, clustering_mode='fitting_data'):
    """
    基于聚类的组合生成（新版：分层过滤与策略组合）。

    Args:
        pec_stress_combo: PEC-Stress组合
        state_offset_wl_fbc: State-offset-WL-FBC映射字典, key是wl_idx
        idx_to_wl_key: 当前PEC的索引到WL Key的映射
        state_cols: State列列表
        all_wl_keys: 所有WL键集合
        num_clusters: 聚类数量
        bestoffset_data: bestoffset数据DataFrame（当clustering_mode='bestoffset'时使用）
        clustering_mode: 聚类模式 ('fitting_data' 或 'bestoffset')

    Returns:
        list: 生成的offset组合列表
    """
    import gc

    print(f"                  \033[36m🔄 Starting cluster-based combination generation for {len(all_wl_keys)} WLs\033[0m")

    # 步骤1：WL聚类
    effective_clusters = min(num_clusters, max(3, len(all_wl_keys) // 25)) if len(all_wl_keys) > 25 else 3

    # 安全转换：将wl_idx映射的字典转换为wl_key映射的字典，用于聚类
    state_offset_wlkey_fbc = {}
    for state, offset_dict in state_offset_wl_fbc.items():
        state_offset_wlkey_fbc[state] = {}
        for offset, wl_idx_dict in offset_dict.items():
            wl_key_dict = {idx_to_wl_key.get(wl_idx, None): fbc for wl_idx, fbc in wl_idx_dict.items()}
            # 过滤掉可能因映射不完整产生的None键
            state_offset_wlkey_fbc[state][offset] = {k: v for k, v in wl_key_dict.items() if k is not None}

    if clustering_mode == 'bestoffset' and bestoffset_data is not None and not bestoffset_data.empty:
        print(f"                    \033[33m📋 Using bestoffset-based clustering mode\033[0m")
        clusters = cluster_wls_by_bestoffset(bestoffset_data, state_cols, effective_clusters)
    elif clustering_mode == 'bestoffset_fbc_enhanced' and bestoffset_data is not None and not bestoffset_data.empty:
        print(f"                    \033[33m📋 Using bestoffset-FBC-enhanced clustering mode\033[0m")
        clusters = cluster_wls_by_bestoffset_fbc_enhanced(bestoffset_data, state_cols, effective_clusters)
    else:
        print(f"                    \033[33m📋 Using fitting_data-based clustering mode\033[0m")
        clusters = cluster_wls_by_fitting_data(pec_stress_combo,state_offset_wlkey_fbc, state_cols, effective_clusters)

    cluster_sizes = [len(wls) for wls in clusters.values() if wls]
    print(f"                    \033[32m✅ WL clustering completed: {len(cluster_sizes)} clusters with sizes {cluster_sizes}\033[0m")

    # 步骤2 & 3: 为每个聚类生成多样化候选，并汇集到全局候选池
    final_state_candidates = {state: set() for state in state_cols}
    for _, cluster_wls in clusters.items():
        if not cluster_wls:
            continue
        
        # 为当前聚类获取在每个state下的多样化候选offset
        cluster_state_offsets = get_diverse_state_offsets_for_cluster(
            cluster_wls, state_offset_wlkey_fbc, state_cols, top_n_by_fbc=2, bestoffset_data=bestoffset_data
        )
        
        # 汇集到全局候选池
        for state, offsets in cluster_state_offsets.items():
            final_state_candidates[state].update(offsets)
            
        del cluster_wls, cluster_state_offsets
        gc.collect()

    # 释放clusters以节省内存
    del clusters
    gc.collect()

    # 步骤4: 对全局候选池进行组合
    product_inputs = []
    for state in state_cols:
        candidates = sorted(list(final_state_candidates[state]))
        if not candidates:
            candidates = [0] # 确保即使没有候选，也有默认值0
        product_inputs.append(candidates)
        print(f"                    \033[36m🔧 State '{state}' has {len(candidates)} candidates: {candidates}\033[0m")

    # 执行笛卡尔积
    offset_combinations = list(itertools.product(*product_inputs))

    print(f"                    \033[32m✅ Generated {len(offset_combinations)} unique offset combinations from all cluster strategies (no global filtering).\033[0m")
    
    # 释放内存
    del final_state_candidates, product_inputs, state_offset_wlkey_fbc
    gc.collect()

    return offset_combinations

def evaluate_combo_batch_shared(task):
    """
    使用共享内存的批量评估函数
    优化：避免大数据结构在进程间的重复传输，显著减少内存使用和提升性能

    Args:
        task: 包含评估所需参数的元组
            - combo_batch: offset组合列表
            - pec_stress_combos: PEC-Stress组合列表
            - state_cols: State列名列表
            - shared_metadata: 共享内存元数据

    Returns:
        list: [(combo, pec, stress, performance), ...] 或 None（如果出错）
    """
    fbc_shm = None
    pec_info_shm = None
    try:
        # 解包任务参数
        combo_batch, pec_stress_combos, state_cols, shared_metadata = task

        # 每个worker进程仍需要pickle.loads()反序列化完整数据，只加载一次用于所有batch
        # pickle.loads() 的作用是将共享内存中的二进制数据“反序列化”，加载成一个完整的 Python 对象
        # 反序列化后的数据在每个进程中都有完整副本
        # 主进程汇总结果时，all_offset_combinations仍然完整存储所有结果

        # 从共享内存加载FBC映射数据（每个进程只执行一次）
        fbc_shm = shared_memory.SharedMemory(name=shared_metadata['fbc_mapping']['name'])
        fbc_data = pickle.loads(fbc_shm.buf[:shared_metadata['fbc_mapping']['size']])

        # 从共享内存加载PEC信息映射数据（每个进程只执行一次）
        pec_info_shm = shared_memory.SharedMemory(name=shared_metadata['pec_info_map']['name'])
        pec_info_map = pickle.loads(pec_info_shm.buf[:shared_metadata['pec_info_map']['size']])

        results = []

        # 处理批次中的每个combo（保持原有逻辑完全不变）
        for combo in combo_batch:
            for pec, stress in pec_stress_combos:
                if (pec, stress) not in fbc_data or not fbc_data[(pec, stress)]:
                    continue

                # 从pec_info_map获取当前PEC的WL信息
                pec_info = pec_info_map.get(pec)
                if not pec_info: continue
                
                wl_idxs = set(range(pec_info['num_wls']))
                num_unique_wls = pec_info['num_wls']
                state_offset_wl_fbc = fbc_data[(pec, stress)]

                # 调用原始的单个combo评估逻辑
                performance = evaluate_single_combo(combo, pec, stress, state_cols, wl_idxs, state_offset_wl_fbc, num_unique_wls)
                if performance is not None:
                    results.append((combo, pec, stress, performance))

        return results

    except Exception as e:
        print(f"Error in evaluate_combo_batch_shared: {e}")
        return None
    finally:
        # 清理本地共享内存引用
        if fbc_shm:
            fbc_shm.close()
        if pec_info_shm:
            pec_info_shm.close()

def evaluate_single_combo(combo, pec, stress, state_cols, wl_idxs, state_offset_wl_fbc, num_unique_wls):
    """
    评估单个offset组合在单个PEC-Stress条件下的表现。
    从原始evaluate_combo函数中提取的核心逻辑。

    优化：使用对象池减少字典创建开销，提升性能
    内存优化：使用wl_idx替代wl_key，减少60-80%内存占用

    Args:
        wl_idxs: WL索引集合，替代原来的wl_keys以节省内存
        num_unique_wls: 当前PEC的唯一WL数量，用于创建固定大小的Numpy数组

    Returns:
        dict: performance字典 或 None（如果出错）
    """
    # 使用对象池获取性能字典，减少内存分配开销
    performance = performance_pool.get_performance_dict()

    try:
        # 边界情况：如果该PEC-Stress条件下没有WL数据，返回空表现
        if not wl_idxs:
            performance.update({
                # 'covered_keys': set(),
                # 'wl_fbc_map': {},
                'fbc_vector': np.full(num_unique_wls, -1, dtype=np.int32),
                'total_wl_count': 0,
                'all_fbc_max': 0,
                'all_fbc_mean': 0
            })
            return performance.copy()  # 返回副本，保留原对象用于回收

        # 1. 遍历所有WL，计算使用当前combo的FBC值（现在使用wl_idx）
        # wl_fbc_map = {}
        fbc_vector = np.full(num_unique_wls, -1, dtype=np.int32) # -1作为无效哨兵值
        for wl_idx in wl_idxs:  # key现在是wl_idx（整数）而不是wl_key（tuple）
            fbc_sum = 0
            valid = True

            # 对每个state，应用对应的offset并累加FBC
            for idx, state in enumerate(state_cols):
                offset = int(combo[idx])  # 获取当前state对应的offset值

                # 检查数据完整性：state和offset是否存在于映射中
                if state not in state_offset_wl_fbc or offset not in state_offset_wl_fbc[state]:
                    valid = False
                    break

                # 获取当前WL在当前state+offset下的FBC值
                fbc = state_offset_wl_fbc[state][offset].get(wl_idx, np.nan)
                if pd.isna(fbc):  # 如果FBC数据缺失，标记为无效
                    valid = False
                    break

                fbc_sum += fbc  # 累加不同state FBC值, 即page FBC

            # 只处理有完整FBC数据的WL
            if valid:
                # wl_fbc_map[wl_idx] = fbc_sum
                fbc_vector[wl_idx] = fbc_sum

        # 2. 基于fbc_vector，派生核心统计指标
        valid_fbcs = fbc_vector[fbc_vector != -1]

        # 计算所有WL的FBC统计（overall）- CSV输出必需
        all_fbc_max = int(np.max(valid_fbcs)) if valid_fbcs.size > 0 else 0
        all_fbc_mean = float(np.mean(valid_fbcs)) if valid_fbcs.size > 0 else 0

        # 3. 构建精简的性能统计字典（内存优化版 - 仅保留核心字段）
        performance.update({
            # 'covered_keys': covered_keys,         # RR选择算法必需 - 内存优化：移至Stage3预计算
            # 'wl_fbc_map': wl_fbc_map,            # RR选择算法必需（现在键是wl_idx）
            'fbc_vector': fbc_vector,            # RR选择算法必需（NumPy数组）
            'total_wl_count': len(wl_idxs),      # CSV输出必需
            'all_fbc_max': all_fbc_max,          # CSV输出必需 (PageFBC_max)
            'all_fbc_mean': all_fbc_mean         # CSV输出必需 (PageFBC_mean)
        })

        # 返回性能统计字典的副本
        result = performance.copy()
        return result

    except Exception as e:
        # 在并行进程中打印错误很重要，因为主进程可能看不到子进程的错误
        print(f"Error in evaluate_single_combo for combo {combo}, PEC {pec}, Stress {stress}: {e}")
        return None

    finally:
        # 将对象归还到对象池
        performance_pool.return_performance_dict(performance)


def get_covered_indices_dynamic(fbc_vector, fbc_criterion):
    """
    动态计算covered indices，返回set

    Args:
        fbc_vector: NumPy数组，包含FBC值
        fbc_criterion: FBC阈值

    Returns:
        set: 满足条件的WL索引集合
    """
    if fbc_vector is None:
        return set()
    covered_indices = np.where((fbc_vector >= 0) & (fbc_vector <= fbc_criterion))[0]
    return set(covered_indices.tolist())


def select_comprehensive_offset_combinations(all_offset_combinations, pec_stress_combos, pec_info_map, target_count=8):
    """
    V4: 基于帕累托最优和分桶优先级策略的选择算法 (NumPy向量化版)
    核心目标：
    1. 覆盖的WL越多越好 (New_Coverage_Count)
    2. 每个WL的FBC越低越好 (Total_FBC_Improvement)
    优化:
    - 使用NumPy向量化操作替代字典循环，极大提升性能
    - 内存结构与PEC独立WL空间对齐
    - 按需计算covered_keys，避免大量内存占用

    Args:
        all_offset_combinations: 所有offset组合及其在各PEC-Stress下的表现 (密集矩阵)
        pec_stress_combos: PEC-Stress组合列表
        pec_info_map: 包含每个PEC的WL映射和数量信息的字典
        target_count: 目标组合数量

    Returns:
        list: 选中的综合offset组合信息
        示例：一个完整的selected_combos_info元素
        {
            'combo': (0, 0, 0, 0),  # 全零基准组合
            'pec_stress_performance': {
                (1000, 0): {
                    'fbc_vector': np.array([...]), # PageFBC向量
                    'total_wl_count': 100,
                    'all_fbc_max': 320,
                    'all_fbc_mean': 210.3
                },
            }
        }
    """
    if not all_offset_combinations:
        return [], {}

    candidate_combos = set(all_offset_combinations.keys())
    num_states = len(next(iter(candidate_combos))) if candidate_combos else 0
    zero_combo = tuple([0] * num_states)

    # 移除预计算covered_keys，改为按需动态计算以节省内存

    # 1. 初始化状态跟踪字典
    selected_combos_info = []
    # 联合覆盖状态
    current_union_coverage = {
        (pec, stress): set() for pec, stress in pec_stress_combos
    }
    # 每个WL的最佳FBC状态 (NumPy向量化版)
    current_best_wl_fbc = {
        (pec, stress): np.full(pec_info_map[pec]['num_wls'], np.iinfo(np.int32).max, dtype=np.int32)
        for pec, stress in pec_stress_combos if pec in pec_info_map
    }
    # 整体最大FBC状态 - (此项在V4中不再需要，可从向量动态计算)

    # 2. 强制添加全零组合作为基准并初始化状态
    if zero_combo in all_offset_combinations:
        zero_combo_perf_map = all_offset_combinations[zero_combo]
        selected_combos_info.append({
            'combo': zero_combo,
            'pec_stress_performance': zero_combo_perf_map
        })

        for pec, stress in pec_stress_combos:
            if pec not in pec_info_map: continue
            perf = zero_combo_perf_map.get((pec, stress), {})
            if perf:
                # 动态计算zero combo的covered_keys
                current_union_coverage[(pec, stress)] = get_covered_indices_dynamic(perf.get('fbc_vector'), FBC_criterion)
                # 使用向量进行初始化
                if 'fbc_vector' in perf and perf['fbc_vector'] is not None:
                    # 使用np.minimum确保只更新有效值，无效值(-1)不影响
                    initial_vector = perf['fbc_vector']
                    # 创建一个与best_fbc相同大小、填充了inf的临时向量
                    temp_inf_vector = np.full_like(current_best_wl_fbc[(pec, stress)], np.iinfo(np.int32).max, dtype=np.int32)
                    # 只在initial_vector有效的地方(-1)使用其值
                    valid_mask = initial_vector != -1
                    temp_inf_vector[valid_mask] = initial_vector[valid_mask]
                    # 更新best_fbc
                    current_best_wl_fbc[(pec, stress)] = temp_inf_vector
        
        if zero_combo in candidate_combos:
            candidate_combos.remove(zero_combo)

    # 3. 迭代选择剩余组合
    num_to_select = min(target_count - 1, len(candidate_combos)) if target_count > 0 else len(candidate_combos)

    # 如果需要全选，则跳过迭代，直接全盘接纳
    if num_to_select >= len(candidate_combos):
        print(f"                \033[33m⚠️  Target count is high, selecting all remaining {len(candidate_combos)} candidate combos.\033[0m")
        for combo in candidate_combos:
            # 直接添加到selected_combos_info
            selected_combos_info.append({
                'combo': combo,
                'pec_stress_performance': all_offset_combinations[combo]
            })

            # 更新所有vendorRR的联合覆盖和最佳FBC状态
            combo_perf_map = all_offset_combinations[combo]
            for pec, stress in pec_stress_combos:
                if pec not in pec_info_map: continue
                perf = combo_perf_map.get((pec, stress))
                if not perf: continue

                # 动态计算选中组合的covered_keys并更新联合覆盖
                selected_covered = get_covered_indices_dynamic(perf.get('fbc_vector'), FBC_criterion)
                current_union_coverage[(pec, stress)].update(selected_covered)

                # 更新每个WL的最佳FBC (NumPy向量化操作)
                best_fbc_vector = current_best_wl_fbc[(pec, stress)]
                combo_fbc_vector = perf.get('fbc_vector')
                if combo_fbc_vector is not None:
                    # 使用np.minimum进行高效更新
                    # 创建一个与best_fbc相同大小、填充了inf的临时向量
                    temp_inf_vector = np.full_like(best_fbc_vector, np.iinfo(np.int32).max, dtype=np.int32)
                    # 只在combo_fbc_vector有效的地方(-1)使用其值
                    valid_mask = combo_fbc_vector != -1
                    temp_inf_vector[valid_mask] = combo_fbc_vector[valid_mask]
                    # 更新best_fbc
                    current_best_wl_fbc[(pec, stress)] = np.minimum(best_fbc_vector, temp_inf_vector)

    else:
        for round_num in range(num_to_select):
            if not candidate_combos:
                break

            # ----------- V4 核心评估逻辑开始 -----------
            round_candidates = []
            for combo in candidate_combos:
                combo_perf_map = all_offset_combinations[combo]
                
                # 计算双指标
                total_new_coverage = 0
                total_fbc_improvement = 0

                for pec, stress in pec_stress_combos:
                    if pec not in pec_info_map: continue
                    perf = combo_perf_map.get((pec, stress))
                    if not perf: continue

                    # 指标A: 新增覆盖数
                    current_covered = current_union_coverage[(pec, stress)]
                    # 动态计算当前组合的covered_keys
                    combo_covered = get_covered_indices_dynamic(perf.get('fbc_vector'), FBC_criterion)
                    new_covered = combo_covered - current_covered
                    total_new_coverage += len(new_covered)

                    # 指标B: FBC总改善度 (NumPy向量化计算)
                    combo_fbc_vector = perf.get('fbc_vector')
                    best_fbc_vector = current_best_wl_fbc[(pec, stress)]

                    if combo_fbc_vector is not None:
                        # 计算改善量 (只考虑combo_fbc_vector有效的部分)
                        valid_mask = combo_fbc_vector != -1
                        improvement = best_fbc_vector[valid_mask] - combo_fbc_vector[valid_mask]

                        # 在第一轮选择中，仅关注对已被全0组合覆盖的WL的FBC改善
                        if round_num == 0:
                            # 1. 获取有效改善值对应的原始WL索引 (这些索引与improvement数组的元素一一对应)
                            valid_indices = np.where(valid_mask)[0]
                            # 2. 从current_covered (一个set) 中，创建一个布尔掩码，标记出那些既有效又已被覆盖的索引
                            covered_mask_for_valid_indices = np.isin(valid_indices, list(current_covered))
                            # 3. 使用此掩码来筛选improvement向量，确保我们只考虑对已覆盖WL的改善
                            improvement = improvement[covered_mask_for_valid_indices]
                            
                        # 只累加正向改善
                        total_fbc_improvement += np.sum(improvement[improvement > 0])
                
                round_candidates.append({
                    'combo': combo,
                    'new_coverage': total_new_coverage,
                    'fbc_improvement': total_fbc_improvement
                })

            if not round_candidates:
                print(f"                \033[33m⚠️  Round {round_num + 1}: No valuable combo found, stopping early\033[0m")
                break

            # ----------- V4 策略三：WL覆盖优先级法 -----------
            bucket_A = [c for c in round_candidates if c['new_coverage'] > 0]
            
            best_combo_this_round = None
            selection_reason = ""

            if bucket_A and (round_num>0):
                # 桶A不为空，优先覆盖：在桶A中选FBC改善最大的
                best_combo_this_round = max(bucket_A, key=lambda x: x['fbc_improvement'])
                selection_reason = f"Coverage-focused: NewCov={best_combo_this_round['new_coverage']}, FBCImprov={best_combo_this_round['fbc_improvement']:.0f}"
            else:
                # 桶A为空，只能优化存量：在所有候选中选FBC改善最大的
                # 第一轮强制选FBC改善最大的，这样可以避免dufault read覆盖WL太多导致少一个好的RR
                best_combo_this_round = max(round_candidates, key=lambda x: x['fbc_improvement'])
                selection_reason = f"Performance-focused: FBCImprov={best_combo_this_round['fbc_improvement']:.0f}"
            
            best_combo_info = best_combo_this_round
            best_combo = best_combo_info['combo']

            # 4. 选中最佳组合并更新状态
            selected_combos_info.append({
                'combo': best_combo,
                'pec_stress_performance': all_offset_combinations[best_combo]
            })

            # 5. 更新联合覆盖和最佳FBC状态
            combo_perf_map = all_offset_combinations[best_combo]
            for pec, stress in pec_stress_combos:
                if pec not in pec_info_map: continue
                perf = combo_perf_map.get((pec, stress))
                if not perf: continue

                # 更新联合覆盖
                # 动态计算选中组合的covered_keys并更新联合覆盖
                selected_covered = get_covered_indices_dynamic(perf.get('fbc_vector'), FBC_criterion)
                current_union_coverage[(pec, stress)].update(selected_covered)

                # 更新每个WL的最佳FBC (NumPy向量化操作)
                best_fbc_vector = current_best_wl_fbc[(pec, stress)]
                combo_fbc_vector = perf.get('fbc_vector')
                if combo_fbc_vector is not None:
                    # 使用np.minimum进行高效更新
                    # 创建一个与best_fbc相同大小、填充了inf的临时向量
                    temp_inf_vector = np.full_like(best_fbc_vector, np.iinfo(np.int32).max, dtype=np.int32)
                    # 只在combo_fbc_vector有效的地方(-1)使用其值
                    valid_mask = combo_fbc_vector != -1
                    temp_inf_vector[valid_mask] = combo_fbc_vector[valid_mask]
                    # 更新best_fbc
                    current_best_wl_fbc[(pec, stress)] = np.minimum(best_fbc_vector, temp_inf_vector)


            candidate_combos.remove(best_combo)
            print(f"                \033[32m✅ Round {round_num + 1}: Selected combo {best_combo} ({selection_reason})\033[0m")

    # 6. 计算联合覆盖率统计
    print("                \033[36m🔄 Computing union coverage statistics...\033[0m")
    union_stats = {}

    for pec, stress in pec_stress_combos:
        if pec not in pec_info_map: continue
        # 直接使用已计算的current_best_wl_fbc和current_union_coverage数据
        best_fbc_vector = current_best_wl_fbc[(pec, stress)]
        union_covered_keys = current_union_coverage[(pec, stress)]

        # 过滤掉无效值(inf)
        valid_best_fbcs = best_fbc_vector[best_fbc_vector != np.iinfo(np.int32).max]
        
        if valid_best_fbcs.size == 0:
            continue

        # 基于每个WL的最优FBC，计算联合统计数据
        union_all_fbc_max = np.max(valid_best_fbcs) if valid_best_fbcs.size > 0 else 0
        union_all_fbc_mean = np.mean(valid_best_fbcs) if valid_best_fbcs.size > 0 else 0

        # 计算覆盖情况（直接使用已有的union_covered_keys）
        total_wls_for_pec = pec_info_map[pec]['num_wls']
        union_coverage_ratio = len(union_covered_keys) / total_wls_for_pec if total_wls_for_pec > 0 else 0

        union_stats[(pec, stress)] = {
            'union_covered_count': len(union_covered_keys),
            'union_total_count': total_wls_for_pec,
            'union_coverage_ratio': union_coverage_ratio,
            'union_all_fbc_max': int(union_all_fbc_max) if union_all_fbc_max > 0 else '',
            'union_all_fbc_mean': f"{union_all_fbc_mean:.1f}" if union_all_fbc_mean > 0 else ''
        }

    print(f"                \033[32m✅ Union coverage statistics computed for {len(union_stats)} PEC-Stress conditions\033[0m")

    return selected_combos_info, union_stats
