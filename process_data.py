import os
import glob
import pandas as pd
import gc
import psutil
import numpy as np
from device_configure import *
from generate_rr_entry import generate_comprehensive_offset_cases_for_pagetype


def get_wlmode_page_state_map(wlmode, device_name):
    """
    Get page_state_map based on WLmode and device_name

    Args:
        wlmode (str): WL mode ('QLC', 'TLC', 'SLC')
        device_name (str): Device name

    Returns:
        dict: Mapping from page_type to state column list (State0X format)
    """
    if wlmode == 'QLC':
        return {
            'LP': ['State01', 'State07', 'State13'],
            'MP': ['State02', 'State06', 'State08', 'State12'],
            'UP': ['State04', 'State09', 'State11', 'State14'],
            'XP': ['State00', 'State03', 'State05', 'State10']
        }
    elif wlmode == 'TLC':
        if device_name == 'BiCS5':
            return {
                'LP': ['State01', 'State04', 'State06'],
                'MP': ['State00', 'State02', 'State05'],
                'UP': ['State03']
            }
        else:
            return {
                'LP': ['State00', 'State04'],
                'MP': ['State01', 'State03', 'State05'],
                'UP': ['State02', 'State06']
            }
    elif wlmode == 'SLC':
        return {
            'LP': ['State00']
        }
    else:
        raise ValueError(f"Unsupported WLmode: {wlmode}")
    
def get_memory_usage():
    """Get current memory usage in MB"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024

def print_memory_info(step_name):
    """Print memory usage information for a specific step"""
    memory_mb = get_memory_usage()
    print(f"    \033[35m🧠 Memory usage after {step_name}: {memory_mb:.1f} MB\033[0m")
    return memory_mb

def clear_memory():
    """Force garbage collection to free memory"""
    gc.collect()

def load_data():
    """Load and merge all CSV files starting with HS, only reading required columns"""
    # Define required columns
    needed_columns = ['SegmentName','Label','Condition', 'Temperature' ,'Channel', 'Ce', 'Lun', 'Block', 'Page', 'Measurement', 'Value']

    csv_files = glob.glob(os.path.join(input_dir, 'HS*.csv'))
    if not csv_files:
        print("  \033[31m❌ No matching CSV files found\033[0m")
        return pd.DataFrame()

    all_data = []
    for file in csv_files:
        try:
            # First read file header to determine available columns
            header = pd.read_csv(file, nrows=0)
            usecols = [col for col in needed_columns if col in header.columns]

            # Only read required columns to reduce memory usage
            df = pd.read_csv(file, usecols=usecols, dtype=str, low_memory=False)
            all_data.append(df)
            print(f"    \033[32m✓ Successfully read file {file}, {len(df)} rows\033[0m")
        except Exception as e:
            print(f"    \033[31m❌ Error reading file {file}: {e}\033[0m")

    if not all_data:
        print("  \033[31m❌ Failed to read any data\033[0m")
        return pd.DataFrame()

    data = pd.concat(all_data, ignore_index=True)
    print(f"  \033[32m✅ Data loading complete, {len(data)} rows total\033[0m")
    return data
    
def save_results_to_csv(data, filename, output_dir='.', is_first_write=True):
    """
    Save chunk data to CSV file with streaming approach

    Args:
        data: DataFrame to save
        filename: Base filename without extension
        output_dir: Output directory
        is_first_write: Whether this is the first chunk (write header)
    Returns:
        str: Full file path
    """
    if data is None or data.empty:
        return None

    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)

    try:
        filepath = os.path.join(output_dir, f'{filename}.csv')
        if is_first_write:
            # First chunk: create new file with header
            data.to_csv(filepath, index=False, encoding='utf-8', mode='w')
            print(f"      \033[36m📄 Created {filename}.csv with {len(data)} rows\033[0m")
        else:
            # Subsequent chunks: append without header
            data.to_csv(filepath, index=False, encoding='utf-8', mode='a', header=False)
            print(f"      \033[36m📄 Appended {len(data)} rows to {filename}.csv\033[0m")
        return filepath

    except Exception as e:
        print(f'      \033[31m❌ Error saving chunk to {filename}.csv: {e}\033[0m')
        return None


def optimize_dataframe_dtypes(df, inplace=True):
    """
    根据预定义的"黄金"映射表，精确地将DataFrame列转换为最优数据类型。
    此函数取代了基于规则的自动推断，以从根源上避免高基数列被错误转换为category类型。

    Args:
        df: DataFrame to optimize
        inplace: If True, modify DataFrame in-place (default: True).
                 Note: The operation is mostly in-place regardless due to astype behavior.

    Returns:
        DataFrame: Optimized DataFrame
    """
    print("  \033[36m🔧 Applying PRECISE dtype optimization based on predefined map...\033[0m")
    original_memory = df.memory_usage(deep=True).sum() / 1024 / 1024

    if not inplace:
        df = df.copy()

    # The "Golden" Dtype Map, based on expert knowledge of the data schema.
    # This is the core of the robust optimization strategy.
    dtype_map = {
        # Low-cardinality string columns -> 'category'
        'ReadType': 'category',
        'Partial_Prog': 'category',
        'PEC': 'category',
        'WLmode': 'category',
        'WLType': 'category',
        'Condition': 'category',
        'Measurement': 'category',
        'State': 'category',

        # High-cardinality or standard numeric columns -> Precise integer types
        stress_col: 'int8',
        'Temperature': 'float32',
        'Channel': 'int8',
        'Ce': 'int8',
        'Lun': 'int8',
        'Block': 'int32',      # CRITICAL: Must be integer to avoid groupby issues
        'Page': 'int32',       # CRITICAL: Must be integer to avoid groupby issues
        'WordLine': 'int32',
        'Level': 'int8',
        'WLGroup': 'int8',
        'Offset': 'int8',
        'FBC': 'float32',
        
        # Columns requiring special handling -> 'object'
        'Value': 'object',       # CRITICAL: Must be object for .str.split()
    }

    converted_cols = []
    for col, target_dtype in dtype_map.items():
        if col in df.columns and df[col].dtype != target_dtype:
            try:
                df[col] = df[col].astype(target_dtype)
                converted_cols.append(f"{col} -> {target_dtype}")
            except (TypeError, ValueError) as e:
                print(f"      \033[33m⚠️  Warning: Could not convert column '{col}' to {target_dtype}: {e}\033[0m")

    optimized_memory = df.memory_usage(deep=True).sum() / 1024 / 1024
    memory_saved = original_memory - optimized_memory

    print(f"  \033[32m✅ Precise optimization completed:\033[0m")
    print(f"    \033[35mOriginal: {original_memory:.1f} MB\033[0m")
    print(f"    \033[35mOptimized: {optimized_memory:.1f} MB\033[0m")
    if original_memory > 0:
        print(f"    \033[32mTotal Saved: {memory_saved:.1f} MB ({memory_saved/original_memory*100:.1f}%)\033[0m")

    if converted_cols:
        print(f"    \033[36mConverted columns: {', '.join(converted_cols)}\033[0m")
    else:
        print("    \033[33mNo columns required conversion.\033[0m")

    return df


# Function to get region based on WordLine value
def get_wordline_group_inhouse(wordline):
    """Return the corresponding region based on WordLine value"""
    for group, (start, end) in Inhouse_WLgroup.items():
        if start <= wordline <= end:
            return group
    return -1  # Unknown group

# Function to get region based on WordLine value
def get_wordline_group_vendor(wordline):
    """Return the corresponding region based on WordLine value"""
    for group, (start, end) in Vendor_WLgroup.items():
        if start <= wordline <= end:
            return group
    return -1  # Unknown group

def get_pec(block_value, pec_definitions, blk_group_config, blk_freq_config):
    """Get PEC name based on Block value and PEC definitions (including extension rules)"""
    if not isinstance(block_value, (int, float)) or pd.isna(block_value):
        return 'INVALID_BLOCK_TYPE'

    block_value = int(block_value) # Ensure integer comparison

    for pec_name, start_base in pec_definitions.items():
        for i in range(blk_group_config):
            current_start_block = start_base + i * blk_freq_config
            current_end_block = current_start_block + test_num_blk_per_pec - 1
            if current_start_block <= block_value <= current_end_block:
                return pec_name
    return 'OTHER_PEC'  # If no matching PEC found

def determine_block_group_index(block_value, base_blk_definitions, group_config, freq_config, num_blk_per_pec_config):
    """Determine the block group index (i) based on Block value"""
    if not isinstance(block_value, (int, float)) or pd.isna(block_value):
        return None

    block_value = int(block_value)

    for start_base in base_blk_definitions.values(): # test_base_blk values are now directly start_base
        for i in range(group_config):
            current_start_block = start_base + i * freq_config
            current_end_block = current_start_block + num_blk_per_pec_config - 1
            if current_start_block <= block_value <= current_end_block:
                return i # Return block group index
    return None # No matching block group index found

def get_partial_prog_ratio(row, rel_test_config, partial_prog_ratios_config):
    if "Partial" not in rel_test_config:
        return "100%"

    channel = row.get('Channel')
    ce = row.get('Ce')
    lun = row.get('Lun')
    block_group_idx = row.get('BlockGroupIndex')

    if channel is None or ce is None or lun is None:
        return "PARTIAL_PROG_KEY_COMPONENT_MISSING"

    # Ensure key components are strings
    key = f"{str(channel)}_{str(ce)}_{str(lun)}"

    # Get ratios_list corresponding to the key from partial_prog_ratios_config
    ratios_list = partial_prog_ratios_config.get(key)
    
    return ratios_list[block_group_idx]


def preprocess_data(data):
    """Data preprocessing: filter Measurement type, calculate WordLine and Level"""
    if data.empty:
        return data

    # 1. Filter Measurement type
    measurement_types = ['FAILS4CHUNK', 'vt_list', 'fail_list', 'BestDAC']
    data = data[data['Measurement'].isin(measurement_types)]

    # 2. Filter bad blocks
    if 'Channel' in data.columns and 'Ce' in data.columns and 'Lun' in data.columns and 'Block' in data.columns:
        # Convert Block to numeric for comparison
        data.loc[:, 'Block'] = pd.to_numeric(data['Block'], errors='coerce')

        # Create channel_ce_lun_block combination string for each row
        data_key = (data['Channel'].astype(str) + '_' +
                   data['Ce'].astype(str) + '_' +
                   data['Lun'].astype(str) + '_' +
                   data['Block'].astype(str))

        # Filter out bad blocks using set lookup (much faster)
        original_count = len(data)
        data = data[~data_key.isin(bad_block)]
        filtered_count = len(data)
        print(f"    \033[33m🚫 Bad blocks filtered: {original_count - filtered_count} rows removed\033[0m")
    else:
        print("    \033[33m⚠️  Warning: Required columns for bad block filtering not found\033[0m")

    # 3. Calculate WordLine/Level/WLmode (if Page column exists)
    if 'Page' in data.columns and not data.empty:
        data = data.copy()
        # Convert Page to numeric type
        data.loc[:, 'Page'] = pd.to_numeric(data['Page'], errors='coerce')
        # Calculate WordLine and Level
        if device_name == 'X36070':
            data.loc[:, 'WordLine'] = data['Page'].apply(lambda x: page_info(x)[3])
            data.loc[:, 'Level'] = data['Page'].apply(lambda x: page_info(x)[1])
            data.loc[:, 'WLmode'] = data['Page'].apply(lambda x: page_info(x)[4])
        else:
            data.loc[:, 'WordLine'] = data['Page'] // 3
            data.loc[:, 'Level'] = data['Page'] % 3
            data.loc[:, 'WLmode'] = 'TLC'
    else:
        print("    \033[33m⚠️  Warning: 'Page' column does not exist\033[0m")

    # 4. Add PEC column based on Block column
    if 'Block' in data.columns:
        # Block column already converted to numeric in step 2
        data.loc[:, 'PEC'] = data['Block'].apply(lambda x: get_pec(x, test_base_blk, blk_group, blk_freq))
        print("    \033[32m✓ PEC column added\033[0m")

        # Add BlockGroupIndex column
        # test_base_blk, blk_group, blk_freq, test_num_blk_per_pec are imported from device_configure
        data.loc[:, 'BlockGroupIndex'] = data['Block'].apply(lambda x: determine_block_group_index(x, test_base_blk, blk_group, blk_freq, test_num_blk_per_pec))
        print("    \033[32m✓ BlockGroupIndex column added\033[0m")
    else:
        print("    \033[33m⚠️  Warning: 'Block' column does not exist\033[0m")

    # 5. Add Partial_Prog column based on Block column and Channel_Ce_Lun
    if 'Partial' in rel_test: # rel_test from device_configure
        # test_partial_prog_ratio from device_configure
        # get_partial_prog can now be obtained from row
        data.loc[:, 'Partial_Prog'] = data.apply(get_partial_prog_ratio, axis=1, rel_test_config=rel_test, partial_prog_ratios_config=test_partial_prog_ratio)
        print("    \033[32m✓ Partial_Prog column added\033[0m")
    else:
        data.loc[:, 'Partial_Prog'] = '100%'
        print("    \033[32m✓ Partial_Prog column added (default 100%)\033[0m")

    # 6. WordLine region divided by Wordline_Region
    data['WLGroup'] = data['WordLine'].apply(get_wordline_group_inhouse)
    print("    \033[32m✓ WLGroup column added\033[0m")

    # 7. WLType divided
    # Get unique WordLine list corresponding to each test_partial_prog_ratio
    # If current WordLine + string_number is in unique WordLine list, then current Word Line belongs to current partial_prog's Inner WL, otherwise Edge WL
    for prog_ratio in data['Partial_Prog'].unique():
        if prog_ratio == '100%':
            data.loc[data['Partial_Prog'] == prog_ratio, 'WLType'] = "InnerWL"
        else:
            unique_WL_list = data[data['Partial_Prog'] == prog_ratio]['WordLine'].unique()
            data.loc[data['Partial_Prog'] == prog_ratio, 'WLType'] = data['WordLine'].apply(lambda x: 'InnerWL' if int(x) + string_number in unique_WL_list else 'EdgeWL')
    print("    \033[32m✓ WLType column added\033[0m")

    # 8. Get stress from SegmentName
    if 'BlkRD' in rel_test:
        data.loc[:, stress_col] = data['SegmentName'].str.split('_').str[-1].str.extract(r'(\d+)').astype(int)/1000
    elif 'SPRD' in rel_test:
        data.loc[:, stress_col] = data['SegmentName'].str.split('_').str[-1].str.extract(r'(\d+)').astype(int)/1000000
    elif 'DR' in rel_test:
        data.loc[:, stress_col] = data['SegmentName'].str.split('_').str[-1].str.extract(r'(\d+)').astype(int)
    print(f"    \033[32m✓ {stress_col} column added\033[0m")

    # 9. Get ReadType from Label
    data.loc[:, 'ReadType'] = data['Label'].apply(lambda x: 'DefaultRead' if pd.notna(x) and 'Default Read' in str(x) else 'BestOffsetRead')
    print("    \033[32m✓ ReadType column added\033[0m")

    # 10. Drop unnecessary columns
    data = data.drop(columns=['BlockGroupIndex','SegmentName'])

    # 11. Reorder columns
    data = data[[stress_col, 'Temperature','ReadType', 'Partial_Prog', 'PEC', 'Channel', 'Ce', 'Lun', 'Block', 'Page', 'WordLine', 'Level', 'WLmode', 'WLType', 'WLGroup','Condition','Measurement', 'Value']]

    print(f"  \033[32m✅ Data preprocessing completed!\033[0m")

    # Save preprocessed data
    save_results_to_csv(data, 'preprocessed_data', output_dir=output_dir)

    preprocessed_bestoffset_data = data[data['Measurement'] == 'BestDAC'].copy()
    preprocessed_read_data = data[data['Measurement'] == 'FAILS4CHUNK'].copy()
    preprocessed_fitting_data = data[data['Measurement'].isin(['vt_list', 'fail_list'])].copy()

    optimize_dataframe_dtypes(preprocessed_bestoffset_data, inplace=True)
    optimize_dataframe_dtypes(preprocessed_read_data, inplace=True)
    optimize_dataframe_dtypes(preprocessed_fitting_data, inplace=True)

    print(f"  \033[32m✅ Data saving completed!\033[0m")

    del data
    clear_memory()

    # add for debug algo
    # 假设数据在fitting_data的'vt_list'或'fail_list'列中
    # 1. 将字符串按@分割成列表
    # 2. 选择索引为5倍数的元素(0, 5, 10...)
    # 3. 重新用,连接
    if 1:
        def extract_every_fifth(data_string):
            if pd.isna(data_string):
                return data_string
            
            values = data_string.split('@')
            # 选择索引为0,5,10,...的元素
            selected_values = values[::5]
            # 重新连接，保持@格式
            return ','.join(selected_values)

        # 应用到列
        raw_scan_data = preprocessed_fitting_data.copy()
        raw_scan_data['Value'] = preprocessed_fitting_data['Value'].apply(extract_every_fifth)
        save_results_to_csv(raw_scan_data, 'raw_scan_data_preprocessed', output_dir=output_dir)

    print(f"  \033[32m✅ Data optimization completed!\033[0m")

    return preprocessed_bestoffset_data, preprocessed_read_data, preprocessed_fitting_data

def process_read_data(read_data):
    """Process FAILS4CHUNK data to generate read_data
    Simplify processing flow, use vectorized operations
    """
    # Filter data
    if read_data.empty:
        dtype= {
            # Low-cardinality string columns -> 'category'
            'ReadType': 'category',
            'Partial_Prog': 'category',
            'PEC': 'category',
            'WLmode': 'category',
            'WLType': 'category',
            'Condition': 'category',
            'Measurement': 'category',

            # High-cardinality or standard numeric columns -> Precise integer types
            stress_col: 'int8',
            'Temperature': 'float32',
            'Channel': 'int8',
            'Ce': 'int8',
            'Lun': 'int8',
            'Block': 'int32',      # CRITICAL: Must be integer to avoid groupby issues
            'Page': 'int32',       # CRITICAL: Must be integer to avoid groupby issues
            'WordLine': 'int32',
            'Level': 'int8',
            'WLGroup': 'int8',
            
            # Columns requiring special handling -> 'object'
            'Value': 'object',       # CRITICAL: Must be object for .str.split()
        }
        print("  \033[33m⚠️  bestread_data is empty, load from preprocessed_data.csv\033[0m")
        file = os.path.join(output_dir, 'preprocessed_data.csv')
        read_data = pd.read_csv(file, dtype=dtype, low_memory=True)
        read_data = read_data[read_data['Measurement'] == 'FAILS4CHUNK']

    try:
        # Define columns to keep
        cols_to_keep = [stress_col, 'Temperature','ReadType', 'Partial_Prog', 'PEC', 
                        'Channel', 'Ce', 'Lun', 'Block', 'WordLine', 'Page', 'Level', 
                        'WLmode', 'WLType', 'WLGroup','Value']
        available_cols = [col for col in cols_to_keep if col in read_data.columns]

        # Only keep required columns to reduce memory usage
        read_data = read_data[available_cols].copy()

        # Split Value by @ to expand into multiple rows (use vectorized operations)
        read_data['Value'] = read_data['Value'].str.split('@')
        read_data = read_data.explode('Value')

        # Remove rows with empty Value
        read_data = read_data[read_data['Value'].notna() & (read_data['Value'].str.strip() != '')]

        # Count occurrences of same value
        group_cols = [col for col in available_cols if col != 'Value']
        read_data = read_data.groupby(group_cols + ['Value'], observed=True).size().reset_index(name='Count')

        read_data.rename(columns={'Value': 'FBC'}, inplace=True)

        print(f"  \033[32m✅ read_data processing completed, {len(read_data)} rows total\033[0m")

        # Save read_data
        save_results_to_csv(read_data, 'read_data', output_dir=output_dir)

        del read_data
        clear_memory()

    except Exception as e:
        print(f"  \033[31m❌ Error processing read_data: {e}\033[0m")


def process_bestoffset(bestoffset):
    """Process BestDAC data to generate bestoffset
    Optimize data processing flow, use vectorized operations
    """
    # Filter data
    if bestoffset.empty:
        dtype= {
            # Low-cardinality string columns -> 'category'
            'ReadType': 'category',
            'Partial_Prog': 'category',
            'PEC': 'category',
            'WLmode': 'category',
            'WLType': 'category',
            'Condition': 'category',
            'Measurement': 'category',

            # High-cardinality or standard numeric columns -> Precise integer types
            stress_col: 'int8',
            'Temperature': 'float32',
            'Channel': 'int8',
            'Ce': 'int8',
            'Lun': 'int8',
            'Block': 'int32',      # CRITICAL: Must be integer to avoid groupby issues
            'Page': 'int32',       # CRITICAL: Must be integer to avoid groupby issues
            'WordLine': 'int32',
            'Level': 'int8',
            'WLGroup': 'int8',
            
            # Columns requiring special handling -> 'object'
            'Value': 'object',       # CRITICAL: Must be object for .str.split()
        }
        print("bestoffset is empty, load from preprocessed_data.csv")
        file = os.path.join(output_dir, 'preprocessed_data.csv')
        bestoffset = pd.read_csv(file, dtype=dtype, low_memory=True)
        bestoffset = bestoffset[bestoffset['Measurement'] == 'BestDAC']

    try:
        # Split Value into multiple columns and remove trailing empty values
        if 'Value' in bestoffset.columns and not bestoffset['Value'].isnull().all():

            # Use vectorized operations to process Value column
            # 1. Split string
            split_values = bestoffset['Value'].str.split('@', expand=True)

            # 2. Create state column names
            state_cols = [f'State{i:02d}' for i in range(split_values.shape[1])]
            split_values.columns = state_cols[:split_values.shape[1]]

            # 3. Remove trailing empty value columns
            # Check non-empty value ratio of each column
            non_empty_ratio = split_values.notna().mean()
            # Keep columns with non-empty value ratio greater than threshold
            valid_cols = non_empty_ratio[non_empty_ratio > 0.1].index.tolist()
            split_values = split_values[valid_cols]

            # 4. Convert to numeric type and handle values greater than 127
            for col in split_values.columns:
                # Convert to numeric
                split_values[col] = pd.to_numeric(split_values[col], errors='coerce')
                # Handle values greater than 127
                mask = split_values[col] > 127
                split_values.loc[mask, col] = split_values.loc[mask, col] - 256

            # 5. Merge back to bestoffset
            bestoffset = pd.concat([bestoffset, split_values], axis=1)

            # 6. unpivot by State column
            available_cols = [stress_col, 'Temperature','Partial_Prog', 'PEC', 
                              'Channel', 'Ce', 'Lun', 'Block', 'WordLine', 
                              'WLmode', 'WLType', 'WLGroup']   # 'Page', 'Level'不要，因为只在最后一个level打印的bestdac
            state_cols = [col for col in bestoffset.columns if 'State' in col]
            bestoffset = bestoffset.melt(id_vars=available_cols, value_vars=state_cols, var_name='State', value_name='Offset')
            bestoffset = bestoffset.dropna(subset=['Offset'])

        print(f"bestoffset processing completed, {len(bestoffset)} rows total")

        # Save bestoffset
        save_results_to_csv(bestoffset, 'bestoffset', output_dir=output_dir)

        # del bestoffset
        # clear_memory()

        # return os.path.join(output_dir, 'bestoffset.csv')

        return bestoffset

    except Exception as e:
        print(f"Error processing bestoffset: {e}")
        return pd.DataFrame()
    
def process_chunk_data(chunk_data):
    """
    简化版处理函数，只进行Value列展开和vt_list/fail_list分离，
    不进行Condition列的pivot展开，保持长表格式
    """
    if chunk_data.empty:
        return pd.DataFrame()

    try:
        # Define key columns for processing
        key_cols = [stress_col, 'Temperature', 'Partial_Prog', 'PEC', 
                    'Channel', 'Ce', 'Lun', 'Block', 'WordLine', 'Page', 'Level', 
                    'WLmode', 'WLType', 'WLGroup','Condition', 'Measurement']

        # Process Value column efficiently
        expanded_rows = []

        for _, row in chunk_data.iterrows():
            values = str(row['Value']).split('@')
            measurement = row['Measurement']

            # Create base row data
            base_data = {col: row[col] for col in key_cols if col != 'Measurement'}

            # Add each value as a separate row
            for i, value in enumerate(values):
                if value and value.strip():  # Skip empty values
                    row_data = base_data.copy()
                    row_data['index'] = i
                    row_data['Measurement'] = measurement
                    row_data['value'] = value.strip()
                    expanded_rows.append(row_data)

        if not expanded_rows:
            return pd.DataFrame()

        # Create DataFrame from expanded rows
        exploded_data = pd.DataFrame(expanded_rows)

        # Clear intermediate data
        del expanded_rows
        clear_memory()

        # 只进行第一次pivot：按Measurement分离vt_list和fail_list
        pivot_data = exploded_data.pivot_table(
            index=[col for col in key_cols if col != 'Measurement'] + ['index'],
            columns='Measurement',
            values='value',
            aggfunc='first'
        ).reset_index()

        # Clear exploded data
        del exploded_data
        clear_memory()

        # Reset column names and convert to numeric
        pivot_data.columns.name = None
        for col in ['vt_list', 'fail_list']:
            if col in pivot_data.columns:
                pivot_data[col] = pd.to_numeric(pivot_data[col], errors='coerce') #pd.to_numeric() 会自动选择最合适的数值类型

        # Remove index column
        pivot_data = pivot_data.drop(columns=['index'])

        # 重命名vt_list为Offset
        pivot_data.rename(columns={'vt_list': 'Offset'}, inplace=True)
        pivot_data.rename(columns={'fail_list': 'FBC'}, inplace=True)
        pivot_data.rename(columns={'Condition': 'State'}, inplace=True)

        # 返回长表格式，保留State列
        # 格式：[stress_col, 'Temperature', 'State', 'Channel', 'Ce', 'Lun', 'Block', 'WordLine',
        #       'WLmode', 'PEC', 'Partial_Prog', 'WLGroup', 'WLType', 'Offset', 'FBC']
        return pivot_data

    except Exception as e:
        print(f"Error processing simplified chunk data: {e}")
        return pd.DataFrame()


def calculate_page_min_fbc(processed_data):
    """
    Calculate minimum FBC for each page (sum of minimum FBC across all conditions in the page)

    Page identification: ['Channel', 'Ce', 'Lun', 'Block', 'WordLine', 'Page']
    Preserved attributes: [stress_col, 'Partial_Prog', 'PEC', 'Level', 'WLmode', 'WLType']

    Args:
        processed_data: Processed chunk data with Offset and FBC columns
        stress_value: Current stress level value

    Returns:
        pd.DataFrame: Page-level minimum FBC results
    """
    if processed_data.empty or 'FBC' not in processed_data.columns:
        return pd.DataFrame()

    try:
        # Define page identification columns (unique page identifier)
        page_id_cols = ['Channel', 'Ce', 'Lun', 'Block', 'WordLine', 'Page']

        # Define preserved attribute columns
        preserved_cols = [stress_col, 'Temperature', 'Partial_Prog', 'PEC', 
                          'Level', 'WLmode', 'WLType', 'WLGroup']

        # Check available columns
        available_page_id_cols = [col for col in page_id_cols if col in processed_data.columns]
        available_preserved_cols = [col for col in preserved_cols if col in processed_data.columns]

        if not available_page_id_cols:
            print("    \033[33m⚠️  Warning: No page identification columns available\033[0m")
            return pd.DataFrame()

        # Combine all grouping columns
        all_group_cols = available_page_id_cols + available_preserved_cols

        # Step 1: Group by page + preserved attributes + condition, find minimum FBC for each condition
        condition_min_fbc = processed_data.groupby(all_group_cols + ['State'], observed=True)['FBC'].min().reset_index()

        # Step 2: Group by page + preserved attributes, sum minimum FBC across all conditions
        page_min_fbc = condition_min_fbc.groupby(all_group_cols, observed=True)['FBC'].sum().reset_index()

        page_min_fbc['ReadType'] = 'fitting_best'

        # Release intermediate variable immediately
        del condition_min_fbc

        print(f"      \033[32m✓ Calculated page minimum FBC for {len(page_min_fbc)} pages\033[0m")
        return page_min_fbc

    except Exception as e:
        print(f"      \033[31m❌ Error calculating page minimum FBC: {e}\033[0m")
        # Clean up in case of exception
        if 'condition_min_fbc' in locals():
            del condition_min_fbc
        return pd.DataFrame()

def process_fitting_data(fitting_data, chunk_size_mb=300):
    """
    Process fitting_data from raw data without hierarchical structure
    Focus on memory optimization and direct chunk processing

    Args:
        data: Preprocessed data DataFrame
        chunk_size_mb: Maximum chunk size in MB for processing (default: 300MB)

    Returns:
        pd.DataFrame: Processed fitting_data
    """
    print("  \033[36m🔄 Starting process_fitting_data...\033[0m")
    print_memory_info("initial state")

    if fitting_data.empty:
        dtype= {
            # Low-cardinality string columns -> 'category'
            'ReadType': 'category',
            'Partial_Prog': 'category',
            'PEC': 'category',
            'WLmode': 'category',
            'WLType': 'category',
            'Condition': 'category',
            'Measurement': 'category',

            # High-cardinality or standard numeric columns -> Precise integer types
            stress_col: 'int8',
            'Temperature': 'float32',
            'Channel': 'int8',
            'Ce': 'int8',
            'Lun': 'int8',
            'Block': 'int32',      # CRITICAL: Must be integer to avoid groupby issues
            'Page': 'int32',       # CRITICAL: Must be integer to avoid groupby issues
            'WordLine': 'int32',
            'Level': 'int8',
            'WLGroup': 'int8',
            
            # Columns requiring special handling -> 'object'
            'Value': 'object',       # CRITICAL: Must be object for .str.split()
        }
        file = os.path.join(output_dir, 'preprocessed_data.csv')
        fitting_data = pd.read_csv(file, dtype=dtype, low_memory=True)
        print("    \033[33m📂 Loaded preprocessed_data.csv for process_fitting_data...\033[0m")
        fitting_data = fitting_data[fitting_data['Measurement'].isin(['vt_list', 'fail_list'])]
        print(f"    \033[32m📊 Filtered data: {len(fitting_data)} rows\033[0m")
        print_memory_info("after filtering data")

    # Streaming save flags
    is_first_fitting_chunk = True
    is_first_fbc_chunk = True
    total_processed_rows = 0
    total_fbc_rows = 0

    try:
        # Memory estimation for entire dataset
        estimated_memory_mb = fitting_data.memory_usage(deep=True).sum() / 1024 / 1024
        print(f"    \033[35m🧠 Estimated memory for entire dataset: {estimated_memory_mb:.1f} MB\033[0m")

        # Calculate number of chunks needed
        num_chunks = int(np.ceil(estimated_memory_mb / chunk_size_mb))
        chunk_size = len(fitting_data) // num_chunks
        print(f"    \033[33m📦 Processing in {num_chunks} chunks, ~{chunk_size} rows per chunk\033[0m")

        # Process data in chunks to control memory usage
        for i in range(num_chunks):
            start_idx = i * chunk_size
            end_idx = min((i + 1) * chunk_size, len(fitting_data))
            # Use view instead of copy for chunk processing
            chunk_df = fitting_data.iloc[start_idx:end_idx]

            print(f"\n    \033[36m🔄 Processing chunk {i+1}/{num_chunks}: rows {start_idx}-{end_idx}\033[0m")
            print(f"      \033[32m📊 Chunk data size: {len(chunk_df)} rows\033[0m")

            # Process chunk data
            processed_chunk = process_chunk_data(chunk_df)
            if not processed_chunk.empty:
                # Stream save fitting data immediately
                save_results_to_csv(processed_chunk, 'fitting_data', output_dir, is_first_fitting_chunk)
                total_processed_rows += len(processed_chunk)
                is_first_fitting_chunk = False

                # Calculate page-level minimum FBC for this chunk
                page_fbc_chunk = calculate_page_min_fbc(processed_chunk)
                if not page_fbc_chunk.empty:
                    # Stream save FBC data immediately
                    save_results_to_csv(page_fbc_chunk, 'page_min_fbc_from_fitting_data', output_dir, is_first_fbc_chunk)
                    total_fbc_rows += len(page_fbc_chunk)
                    is_first_fbc_chunk = False

                # Clear page_fbc_chunk immediately after use
                del page_fbc_chunk

            # Clear processed_chunk immediately after use
            del processed_chunk
            clear_memory()
            print_memory_info(f"processing chunk {i+1}")

        # Release fitting_data after all processing is complete
        del fitting_data
        clear_memory()
        print_memory_info("after releasing fitting_data")

        # Summary of streaming save results
        print(f"\n  \033[32m✅ Streaming save completed:\033[0m")
        if total_processed_rows > 0:
            final_csv_path = os.path.join(output_dir, 'fitting_data.csv')
            print(f"    \033[36m� fitting_data.csv: {total_processed_rows} rows saved\033[0m")
        else:
            print("    \033[33m⚠️  No fitting data processed\033[0m")
            final_csv_path = None

        if total_fbc_rows > 0:
            print(f"    \033[36m📄 page_min_fbc.csv: {total_fbc_rows} rows saved\033[0m")
        else:
            print("    \033[33m⚠️  No page FBC data processed\033[0m")

        print_memory_info("final state")

        # Return path instead of large DataFrame
        return final_csv_path

    except Exception as e:
        print(f"  \033[31m❌ Error in process_fitting_data: {e}\033[0m")
        # Clean up in case of exception
        if 'fitting_data' in locals():
            del fitting_data
        clear_memory()
        return None


def generate_cases_integrated(fitting_data_file='fitting_data.csv', bestoffset_data_file='bestoffset.csv',
                              use_clustering = False, clustering_mode='fitting_data',
                              case_num = 5, all_offset_combo_from_vendor=False):
    """
    合并process_fitting_data_optimized和generate_offset_cases的主函数
    新的7层分层结构：
    Level 1: WLmode (QLC/TLC/SLC)
    Level 2: WLType (Inner/Edge)
    Level 3: WLGroup
    Level 4: Partial_Prog
    Level 5: PageType (最终在此分层不同的PageType产生5~10个综合offset组合，能够覆盖不同的PEC/Stress，重点是覆盖高stress/PEC 条件)
    Level 6: PEC (从高PEC开始，解决组合爆炸以及内存不够的问题)
    Level 7: Stress(从高Stress开始，解决组合爆炸以及内存不够的问题)

    特殊处理规则：
    X36070 TLC: 不分WLGroup/ Partial_prog
    X36070 SLC: 不分  WLType/WLGroup/ Partial_prog
    X36070 QLC + 其他设备: 按标准7层结构处理
    """
    print(f"  \033[36m🔄 Starting integrated {fitting_data_file} processing with generate_offset_cases...\033[0m")
    print("    \033[33m📋 Using new 7-level hierarchical structure...\033[0m")
    print_memory_info("initial state")

    # Reload fitting_data and bestoffset_data from files
    dtype= {
        # Low-cardinality string columns -> 'category'
        'Partial_Prog': 'category',
        'PEC': 'category',
        'WLmode': 'category',
        'WLType': 'category',
        'State': 'category',

        # High-cardinality or standard numeric columns -> Precise integer types
        stress_col: 'int8',
        'Temperature': 'float32',
        'Channel': 'int8',
        'Ce': 'int8',
        'Lun': 'int8',
        'Block': 'int32',      # CRITICAL: Must be integer to avoid groupby issues
        'Page': 'int32',       # CRITICAL: Must be integer to avoid groupby issues
        'WordLine': 'int32',
        'Level': 'int8',
        'WLGroup': 'int8',
        'Offset': 'int8',
        'FBC': 'float32',
    }
    fitting_data = pd.read_csv(os.path.join(output_dir, fitting_data_file), dtype=dtype, low_memory=True)  # low_memory=True分块加载数据，可能会导致同一列在不同chunk中被推断为不同类型，使用dtype可以避免这一问题
    bestoffset_data = pd.read_csv(os.path.join(output_dir, bestoffset_data_file), dtype=dtype, low_memory=False)

    print_memory_info(f"load {fitting_data_file} and {bestoffset_data_file}")

    if fitting_data.empty or bestoffset_data.empty:
        print(f"    \033[31m❌ {fitting_data_file} or {bestoffset_data_file} is empty, no processing\033[0m")
        return pd.DataFrame()
    
    # 如果使用vendor RR或者自己需要, WLGroup需要调整
    if all_offset_combo_from_vendor:
        fitting_data['WLGroup'] = fitting_data['WordLine'].apply(get_wordline_group_vendor)
        bestoffset_data['WLGroup'] = bestoffset_data['WordLine'].apply(get_wordline_group_vendor)
        print(f"    \033[32m✓ WLGroup column changed to vendor WLGroup\033[0m")

        # 获取RR table
        vendor_RR_table = pd.read_csv(os.path.join(os.path.dirname(output_dir), 'vendor_RR_table.csv'), low_memory=False)
        vendor_RR_table['WLGroup'] = vendor_RR_table['WLGroup'].astype(str)
        vendor_RR_table['RRNumber'] = vendor_RR_table['RRNumber'].astype(int)
    else:
        vendor_RR_table = None

    # 借用Vendor_WLgroup修改'WLGroup'，可以实现自己想要的效果
    change_wlgroup_by_user = True
    if change_wlgroup_by_user and not all_offset_combo_from_vendor:
        fitting_data['WLGroup'] = fitting_data['WordLine'].apply(get_wordline_group_vendor)
        bestoffset_data['WLGroup'] = bestoffset_data['WordLine'].apply(get_wordline_group_vendor)
        print(f"    \033[32m✓ WLGroup column changed to user defined WLGroup\033[0m")

    # 选择WLmode = "QLC", WLType = "InnerWL", WLGroup = 3, Partial_Prog = 100%, PageType = LP的数据作为demo
    if 0:
        fitting_data = fitting_data[(fitting_data['WLmode'] == 'QLC') & (fitting_data['WLType'] == 'InnerWL') & (fitting_data['WLGroup'] == 1) & (fitting_data['Partial_Prog'] == "100%")]
        print(f"    \033[33m🎯 Selected data for demo: {len(fitting_data)} rows\033[0m")

    # all_results = []

    try:
        # 检查设备特殊处理规则
        is_x36070 = 'X36070' in device_name

        # Level 1: WLmode (QLC/TLC/SLC) - 只获取唯一值，不创建DataFrame
        for wlmode in fitting_data['WLmode'].unique():
            print_memory_info("iterate WLmode")
            print(f"\n    \033[1;34m📊 Level 1 - Processing WLmode: {wlmode}\033[0m")

            # Get page_state_map for current WLmode
            try:
                page_state_map = get_wlmode_page_state_map(wlmode, device_name)
            except ValueError as e:
                print(f"      \033[33m⚠️  Skipping unsupported WLmode {wlmode}: {e}\033[0m")
                continue

            # Level 2: WLType (Inner/Edge) - X36070 SLC跳过此层
            if is_x36070 and wlmode == 'SLC':
                # X36070 SLC: 不分WLType/WLGroup/Partial_prog，直接到PageType
                print(f"      \033[33m⚠️  X36070 SLC: Skipping WLType levels\033[0m")
                wltype_list = ['N/A']
            else:
                # 获取当前WLmode下的WLType唯一值
                wlmode_mask = fitting_data['WLmode'] == wlmode
                wltype_list = fitting_data[wlmode_mask]['WLType'].unique()

            for wltype in wltype_list:
                print_memory_info("iterate WLType")
                if not (is_x36070 and wlmode == 'SLC'):
                    print(f"      \033[1;36m📊 Level 2 - Processing WLType: {wltype}\033[0m")

                # Level 3: WLGroup - X36070 TLC和SLC跳过此层
                if is_x36070 and wlmode in ['TLC', 'SLC']:
                    # X36070 TLC/SLC: 不分WLGroup
                    print(f"        \033[33m⚠️  X36070 {wlmode}: Skipping WLGroup level\033[0m")
                    wlgroup_list = ['N/A']
                else:
                    # X36070 QLC / 其他设备的TLC 按标准处理
                    # 获取当前WLmode+WLType下的WLGroup唯一值
                    wltype_mask = wlmode_mask & (fitting_data['WLType'] == wltype)
                    wlgroup_list = fitting_data[wltype_mask]['WLGroup'].unique()

                for wlgroup in wlgroup_list:
                    print_memory_info("iterate WLGroup")
                    if not (is_x36070 and wlmode in ['TLC', 'SLC']):
                        print(f"        \033[1;32m📊 Level 3 - Processing WLGroup: {wlgroup}\033[0m")

                    # Level 4: Partial_Prog - X36070 TLC和SLC跳过此层
                    if is_x36070 and wlmode in ['TLC', 'SLC']:
                        # X36070 TLC/SLC: 不分Partial_prog
                        print(f"          \033[33m⚠️  X36070 {wlmode}: Skipping Partial_Prog level\033[0m")
                        partial_prog_list = ['N/A']
                    else:
                        # 获取当前WLmode+WLType+WLGroup下的Partial_Prog唯一值
                        wlgroup_mask = wltype_mask & (fitting_data['WLGroup'] == wlgroup)
                        partial_prog_list = fitting_data[wlgroup_mask]['Partial_Prog'].unique()

                    for partial_prog in partial_prog_list:
                        print_memory_info("iterate Partial_Prog")
                        if not (is_x36070 and wlmode in ['TLC', 'SLC']):
                            print(f"          \033[1;33m📊 Level 4 - Processing Partial_Prog: {partial_prog}\033[0m")

                        # Level 5: PageType (最终在此分层产生5~10个综合offset组合，覆盖不同的PEC/Stress)
                        for page_type, state_cols in page_state_map.items():

                            if 0:
                                if page_type != 'XP':
                                    continue

                            print(f"            \033[1;35m📊 Level 5 - Processing PageType: {page_type}, StateCols: {state_cols}\033[0m")

                            # 只在这里才真正筛选数据 - 构建完整的筛选条件
                            conditions = (fitting_data['WLmode'] == wlmode)
                            if wltype != 'N/A':
                                conditions &= (fitting_data['WLType'] == wltype)
                            if wlgroup != 'N/A':
                                conditions &= (fitting_data['WLGroup'] == wlgroup)
                            if partial_prog != 'N/A':
                                conditions &= (fitting_data['Partial_Prog'] == partial_prog)
                            # 筛选需要的State
                            conditions &= fitting_data['State'].isin(state_cols)
                            page_data = fitting_data[conditions]

                            # 为bestoffset_data单独构建筛选条件
                            bestoffset_conditions = (bestoffset_data['WLmode'] == wlmode)
                            if wltype != 'N/A':
                                bestoffset_conditions &= (bestoffset_data['WLType'] == wltype)
                            if wlgroup != 'N/A':
                                bestoffset_conditions &= (bestoffset_data['WLGroup'] == wlgroup)
                            if partial_prog != 'N/A':
                                bestoffset_conditions &= (bestoffset_data['Partial_Prog'] == partial_prog)
                            bestoffset_conditions &= bestoffset_data['State'].isin(state_cols)
                            page_bestoffset_data = bestoffset_data[bestoffset_conditions]

                            if page_data.empty:
                                print(f"              \033[33m⚠️  Skipping: no data for required conditions\033[0m")
                                continue

                            # 在PageType层级生成综合offset组合，覆盖不同的PEC/Stress条件
                            # 重点覆盖高stress/PEC条件
                            print(f"              \033[36m🔄 Generating comprehensive offset combinations for PageType: {page_type}\033[0m")

                            # Build group information for PageType level
                            group_info = {
                                'WLmode': wlmode,         # Level 1
                                'WLType': wltype,         # Level 2
                                'WLGroup': wlgroup,       # Level 3
                                'Partial_Prog': partial_prog,  # Level 4
                                'PageType': page_type,    # Level 5
                            }

                            # 生成综合offset cases，覆盖不同PEC/Stress条件
                            result = generate_comprehensive_offset_cases_for_pagetype(
                                page_data, state_cols, group_info, stress_col,
                                use_clustering=use_clustering,
                                clustering_mode=clustering_mode,
                                bestoffset_data = page_bestoffset_data,
                                case_num = case_num,
                                all_offset_combo_from_vendor=all_offset_combo_from_vendor,
                                vendor_RR_table=vendor_RR_table
                            )
                            if result:
                                csv_path = os.path.join(output_dir,'offset_cases_integrated.csv')
                                is_first_write = True if os.path.isfile(csv_path) else False
                                df = pd.DataFrame(result)
                                print(f"\n  \033[32m✅ Total generated {len(df)} offset combination records\033[0m")
                                save_results_to_csv(df, 'offset_cases_integrated', output_dir, is_first_write)

                            # 释放PageType级别内存
                            del page_data, page_bestoffset_data
                            gc.collect()

        # 输出结果
        # if all_results:
        #     df = pd.DataFrame(all_results)
        #     print(f"\n  \033[32m✅ Total generated {len(df)} offset combination records\033[0m")
        #     save_results_to_csv(df, 'offset_cases_integrated', output_dir=output_dir)
        # else:
        #     print("  \033[33m⚠️  No offset combinations found that meet the conditions\033[0m")

    except Exception as e:
        print(f"  \033[31m❌ Error in integrated processing: {e}\033[0m")
